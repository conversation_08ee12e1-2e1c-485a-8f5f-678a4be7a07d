import os
from pathlib import Path

# 优先使用 tomli，否则 fallback 到 toml
try:
    import tomli as toml_loader
    _TOML_LIB = "tomli"
except ImportError:
    import toml as toml_loader
    _TOML_LIB = "toml"

def _get_env():
    # 优先 APP_ENV，其次 ENV，均不区分大小写
    env = os.environ.get("APP_ENV") or os.environ.get("ENV") or ""
    env = env.strip().lower()
    if env in ("dev", "development"):
        return "dev"
    return "prod"

def _get_config_path():
    base_dir = Path(__file__).parent
    env = _get_env()
    if env == "dev":
        dev_path = base_dir / "config.dev.toml"
        if dev_path.exists():
            return dev_path
        # fallback 到 config.toml
    return base_dir / "config.toml"

_config_cache = None

def _load_config():
    global _config_cache
    if _config_cache is not None:
        return _config_cache
    config_path = _get_config_path()
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")
    try:
        if _TOML_LIB == "tomli":
            with config_path.open("rb") as f:
                # tomli 只支持二进制
                data = toml_loader.load(f)
        else:
            with config_path.open("r", encoding="utf-8") as f:
                # toml 只支持文本模式
                data = toml_loader.load(f)
    except Exception as e:
        raise RuntimeError(f"Failed to parse config file {config_path}: {e}")
    _config_cache = data
    return _config_cache

# 单例暴露 config 变量
config = _load_config()

def get_postgres_config() -> dict:
    """
    获取 Postgres 数据库配置项，返回 dict。
    包含字段：host, port, user, password, database
    """
    return config.get("postgres", {})

def get_minio_config() -> dict:
    """
    获取 MinIO S3 配置项，返回 dict。
    包含字段：endpoint, access_key, secret_key, bucket, region（可选）
    """
    return config.get("minio", {})
def get_voice_map() -> dict:
    """
    获取还原后的 VOICE_MAP 配置，结构为 dict[language][role]=list。
    来源于 config.toml 的 [voice_map] 配置。
    兼容两种结构：
      1. 旧结构：["voice1", "voice2"]，自动转为 [{"key": "voice1", "desc": ""}, ...]
      2. 新结构：[{"key": "...", "desc": "..."}]，直接返回，若缺少 desc 字段自动补空字符串。
    返回值示例：
      {
        "zh": {
          "0": [{"key": "...", "desc": "..."}],
          "1": [{"key": "...", "desc": "..."}]
        },
        ...
      }
    """
    # voice_map 结构已统一为对象数组，每项含 key/desc，兼容旧字符串数组格式
    vm = config.get("voice_map", {})
    result = {}
    for lang, roles in vm.items():
        role_map = {}
        for role, voices in roles.items():
            # 兼容字符串数组
            if isinstance(voices, list) and voices and isinstance(voices[0], str):
                role_map[str(role)] = [{"key": v, "desc": ""} for v in voices]
            # 兼容对象数组
            elif isinstance(voices, list) and voices and isinstance(voices[0], dict):
                # 补全缺失字段
                role_map[str(role)] = [
                    {"key": v.get("key", ""), "desc": v.get("desc", "")} for v in voices
                ]
            # 兼容单字符串
            elif isinstance(voices, str):
                role_map[str(role)] = [{"key": voices, "desc": ""}]
            else:
                role_map[str(role)] = []
        result[lang] = role_map
    return result

def get_workflow_url() -> str:
    """
    获取 webhook 基础 URL（不含具体路径），来源于 config.toml 的 [api] workflow_url 字段。
    """
    workflow_conf = config.get("workflow", {})
    return workflow_conf.get("workflow_url", "")