from openai import OpenAI
from typing import List, Dict, Optional
from backend.config import config

_llm_model_instance = None

class LLMModel:
    def __init__(self, model_name: str, api_key: str, base_url: Optional[str] = None):
        if not api_key:
            raise ValueError("API key must be provided for LLMModel.")
        if not model_name:
            raise ValueError("Model name must be provided for LLMModel.")

        self.model_name = model_name
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url
        )

    def generate(self, messages: List[Dict[str, str]], max_tokens: int = 3000, temperature: float = 0.7) -> str:
        """
        Generates text using the configured LLM.
        Args:
            messages: A list of message objects, e.g., [{"role": "user", "content": "Hello!"}]
            max_tokens: The maximum number of tokens to generate.
            temperature: The sampling temperature.
        Returns:
            The generated text content.
        """
        try:
            completion = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )
            return completion.choices[0].message.content
        except Exception as e:
            print(f"Error during LLM generation: {e}")
            # It's often better to let the caller handle the exception or return a specific error indicator
            raise

    @classmethod
    def get_instance(cls):
        global _llm_model_instance
        if _llm_model_instance is None:
            print("Initializing LLMModel instance...")
            try:
                api_key = config["ai"]["LLM_API_KEY"]
            except KeyError:
                raise ValueError("LLM_API_KEY not set in config. Please set it in your config.toml file.")
    
            model_name = config["ai"]["LLM_MODEL_NAME"]
            base_url = config["ai"]["LLM_BASE_URL"]
    
            _llm_model_instance = cls(model_name=model_name, api_key=api_key, base_url=base_url)
            print(f"LLMModel instance initialized with model: {model_name}, base_url: {'default OpenAI' if not base_url else base_url}.")
        return _llm_model_instance