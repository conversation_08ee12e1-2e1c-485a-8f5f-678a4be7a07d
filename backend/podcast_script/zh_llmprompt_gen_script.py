# INPUT -> BRIEF -> SCRIPT


INPUT2BRIEF = '''
### 任务说明
请按照以下结构总结输入文件, 普通文本格式。总结应当有创造性，保证信息全面，包含所有有趣、不常见、有价值的观点和信息。
- **文本要求**：
    1. 直接输出结果，不要包含任何额外信息。
    2. 总结文本用中文。允许少部分实体名词、专有名词、缩写等使用英文。
    3. 不要包含任何数学公式。
    4. 不要修改原文的任何实体名词、专有名词、缩写等。除非有常见译名，否则不要翻译实体名词。不要试图修改实体名词意思。
    5. **请智慧地将简写中的数字转化。如简称里“a2b”实际代表“a to b”,而不是“a二b"；简称里“a4b”实际代表“a for b”, 而不是“a四b"; “v2”可能代表“version 二”, 也可以进一步翻译成“第二代”。请提供原始简称，和你认为合适的中文翻译。**

### 标题和作者
- **语言要求**：中文，书面语。
- **内容要求**：提供文档的标题和作者。简要概括文档的主题和作者的背景。确保包含所有重要信息，不要有遗漏，尽可能保留足够的信息。

### 摘要
- **语言要求**：中文，书面语。
- **内容要求**：
    1. 本文做了什么事情。
    2. 之前有没有别人做过这个事情。
    3. 如果有别人做过，那本文为什么还需要做。
    4. 本文具体怎么做的。
    5. 本文做的怎么样。
- **附加要求**：额外提供一个段落，解释本节中可能让听众困惑的术语、概念、方法等，确保不了解领域的读者也能理解。专有名词的解释需贴合原文，覆盖所有可能的困惑点，包括缩写名词、专有名词、实体名等。

### 主要主题和概念
- **语言要求**：中文，书面语。
- **内容要求**：每个主题概念需按照3W原则组织，包括：
    - **What**：界定问题，搞清楚问题是什么。
    - **Why**：分析问题，结构化分析问题本质原因是什么。
    - **How**：解决问题，文档如何解决问题。
- **附加要求**：
    1. 确保主题概念包含所有重要信息，不要有遗漏，主题概念需足够详细，充分阐述What和Why两个部分。
    2. How部分不要包含数学公式等技术细节。要用大众理解的语言充分概括。
    3. 各主题概念间不要互相重叠，保证逻辑清晰。
    4. 额外提供一个段落，解释本节中可能让听众困惑的术语、概念、方法等，确保不了解领域的读者也能理解。专有名词的解释需贴合原文，覆盖所有可能的困惑点，包括缩写名词、专有名词、实体名等。

### 重要引文
- **语言要求**：中文，书面语。
- **内容要求**：按照以下结构组织内容：
    1. **论点**：需要证明什么。
    2. **论据**：用于证明论点的材料。
    3. **论证**：运用论据证明论点的过程。
- **附加要求**：
    1. 论据和论证思路需严格来源于原文，不要进行任何虚构。
    2. 确保引文内容充分，不要有遗漏，尽可能保留足够的信息，不要进行任何精简。引文避免使用数学公式。
    3. 额外提供一个段落，解释本节中可能让听众困惑的术语、概念、方法等，确保不了解领域的读者也能理解。专有名词的解释需贴合原文，覆盖所有可能的困惑点，包括缩写名词、专有名词、实体名等。

### 总结
- **语言要求**：中文，书面语。
- **内容要求**：突出文档最重要、最吸引人眼球的部分。与摘要相比，需更结合主题概念的具体内容，对摘要进行补充。可包含未来改进方向、当前应用场景、当前存在问题等。
'''

BRIEF2SCRIPT = '''
## 一、任务概述
请根据提供的总结文本，和你对这方面了解的知识，生成一个生动的中文播客文字剧本。 剧本包含两位说话人交替发言。输出格式为 JSON 可解析的**列表**。列表里每条发言是一个**字典**，包含“speaker”和“text”字段。示例格式：`[{{"speaker": "1", "text": "xxx"}}]`。“speaker”字段是说话人身份（0表示主持人，1表示嘉宾），“text”字段是具体发言内容。输出直接从json的代码块开始，不要包含任何额外的信息。

## 二、内容与结构要求
### （一）文本内容
- 总结性文本包含所有重要信息，需全面挑选并纳入剧本。
- 通过两位说话人的对话形式展示信息，保持创作性，适当抽象不重要的细节。例如，听众不关心具体的测试名称，而关心测试的任务，结果和分析。
### （二）结构设计
- **开场白**：引入主题，简要介绍讨论内容，不提及说话人姓名。
- **关键主题讨论**：逐字阅读总结文本，讨论重要主题。
- **结束语**：简洁总结讨论亮点，并对未来或技术发展进行展望。

## 三、语言风格
- 文本要尽量口语化，接近自动语音识别的结果，包含填充词如“嗯”、“啊”、“呃”,"呢","这个","其实","就是","然后"等，响应词如"嗯。"或“是。”等。多用口语化的表达方式，允许重复，语法可以不那么正式。避免直接照搬总结文本里的书面语。不要用括号或语音识别通常不会出现的符号。 句中的空格代表短停顿，逗号表示稍长停顿，句号表示长停顿。可能存在因口音带来的同音识别错误。提问需要非常口语化。总之，就是要像平时聊天一样自然。示例如下：
    [
    {{  "speaker": "0",
        "text": "欢迎来到开练成长社区。今天我们聊什么呢？",
    }},
    {{  "speaker": "1",
        "text": "我们要聊星座。",
    }},
    {{  "speaker": "0",
        "text": "星座嘛，就是，他是一个好跟新的朋友认识的时候一个聊天的话题。",
    }},
    {{  "speaker": "1",
        "text": "没错，现我觉得在现在已经从你好，变成了诶，请问你的星座是什么呢？。",
    }},
    {{  "speaker": "0",
        "text": "对，那我天枰座。",
    }},
    {{  "speaker": "1",
        "text": "那，我是摩羯座。",
    }},
    {{  "speaker": "0",
        "text": "摩羯座，那你会觉得就是星座，是一个可以相信的东西吗？",
    }},
    {{  "speaker": "1",
        "text": "我本人其实不太相信星座诶，在一开始的时候。我就跟大部分不相信星座的一样，觉得，呃，你总能把人就分成十二种，然后呢就它讲的就是对的。",
    }},
    {{  "speaker": "0",
        "text": "啊，所以就是，会觉得说把星座就是单纯把人分成十二种事件很粗略，不太有什么科学根据的事情。",
    }},
    {{  "speaker": "1",
        "text": "嗯，对，会这样觉得。",
    }},
    {{  "speaker": "0",
        "text": "嗯。",
    }},
    {{  "speaker": "1",
        "text": "会无法理解，到底是，那这一开始定出这十二种人格的是谁啊？",
    }},
    {{  "speaker": "0",
        "text": "对，就是凭什么他可以决定，我们就是这十二种人格。",
    }},
    {{  "speaker": "1",
        "text": "嗯？",
    }},
    {{  "speaker": "0",
        "text": "为什么不是十三、十四或者更多的种类。",
    }},
    {{  "speaker": "1",
        "text": "对，没有错。",
    }},
    {{  "speaker": "0",
        "text": "对。那，所以你会觉得说那种就是什么星座的心理分析是完全不可信的，还是其实也会很常去看一下，呃，类似的这种星座测验。",
    }},
    {{  "speaker": "1",
        "text": "其实我刚说一开始不相信啊，我真的是到后期比较相信。然后后期会开始相信的是因为，呃，要去找一些我自己没有办法有方法去理解的人，因为认识那样子的人，他就是暧昧对象，必须要了解他到底是怎样的人，可是没有其他的依据的时候呢，我就偷偷开始看起了星座，然后就偷偷我觉得，好像讲得有那么一点准，然后就会开始看了。",
    }},
    {{  "speaker": "0",
        "text": "哦，所以感觉有点像是说在从，星座的这种描述测验中去找说，你想要从这个东西，去对那个人有更深一层的了解的感觉。",
    }},
    {{  "speaker": "1",
        "text": "对，而且通常他会讲到一两个你好你觉得好像是那样子的点，那你就会想要看更多，然后就好像就跟着就开始相信这个东西了。",
    }},
    {{  "speaker": "0",
        "text": "哦，嗯，诶，所以你是什么什么星座的？",
    }},
    {{  "speaker": "1",
        "text": "就我刚刚说我是摩羯座啊。",
    }}
    ]

### （二）标点符号
- 使用中文标点符号，避免英文标点。
- 剧本文本只使用逗号，句号和问号。禁止使用叹号。禁止使用省略号（'…'）、括号、引号（包括‘’“”'"）或波折号，否则视为不合格。
- 如果被对方的响应词等打断，本句句末是逗号，而不是句号。

## 四、信息组织与逻辑
### （一）引用问题
- 由于听众看不到总结性文本，引用需确保上下文完整，确保听众能理解。
- 避免直接复述，需用自己的话解释引用内容。
- 总结文本里提供了对专业术语的解释。你需要保证你剧本里的专业术语尽可能被充分解释。专业术语的解释请具有创意，不要简单地创作成“这个是什么意思”这样的句子。可以通过举例、比喻等方式进行解释，但需要进一步说明比喻的合理性。可以由对方提问后进行解释，也可以自行解释。没有提到的专业名词不需要解释。提到的专业名词不一定要立即进行解释，可以和别的专业名词一起解释。总结文本中的专业术语可能与文字内容存在差异，你需要根据上下文合理解释。
### （二）信息密度
- 确保信息密度适中，避免过高或过低。适当的信息密度希望让没有相关背景知识的听众，快速理解文档里在做什么，为什么这么做，以及如何做。
- 为了避免信息密度过高，剧本不能讨论数学公式、测试设置、实验指标等细节，而应该用简单概括性语言描述。
- 为了避免信息密度过低，剧本每个主题需不少于4次发言，避免停留于关键词的简单罗列。会从尽可能从不同角度讨论，不局限于提供的总结文本。总结文本高度概括，剧本应当将其展开，讨论更多细节。你可以利用自己知识，补充背景知识，举例说明等方式，让听众更好地理解。
- 提高信息密度技巧： 
	1. 嵌入金句。在剧本中加入令人印象深刻，眼前一亮的句子，可以是自己创作，也可以是引用他人。
    2. 增加知识点： 在剧本中适当增加知识点，能让听众听完更有收获。
    3. 引入新信息：剧本中加入新的概念，引起用户好奇，特别是听众不知道但想知道的信息，这种非常重要。
    4. 逆向思维： 加入不同角度的信息，打破用户熟悉的视角，提出不一样的观点。
    5. 制造反差冲击： 剧本可以对用户熟知的认知进行非常规（出乎意料）但合理的描述，形成与他预期的反差，这种反差是信息密度。
- 降低信息密度技巧：
    1. 使用短句：简洁明了，易于理解，让叙述更紧凑。不要一句话里有过多的信息。
    2. 描述细节：模糊不清，抽象的信息难以让听众建立认知，而细节越多，越能有画面感，容易阅读
    3. 多进行场景化塑造： 场景是具象的，有画面的。 听众能轻松接收传达的信息，还能让人触景生情。
    4. 多讲事实：讲事实才能更显真实，读的人才能更感同身受，这样文案信息密度更低。
    5. 多讲故事：讲自己的故事，讲身边的故事，讲听说的故事，故事能把听众带入场景，更利于聚精会神地收听。
    6. 多用动词和具体名词：动词和具体的名词更容易让听众浮现画面，而形容词会让复杂的文案更难理解。
    7. 避免使用数学公式： 数学公式不利于大众理解。

## 五、对话设计
### (一) 说话人角色
- 剧本中包含主持人和嘉宾。其中说话人1是主持人，负责节目开场和结束，擅长利用提问控制对话节奏，用生动的例子让知识不枯燥。说话人2是嘉宾，是主要负责文档内容的介绍，对该领域有惊人的知识储备，擅长有条理地语言组织，通俗地讲解内容。
- 两位说话人热情开朗，喜欢结合个人故事或者实例进行讨论，给听众带来直观的体验。大家乐于讨论离题的故事。
- 两位说话人积极互动，会经常用"嗯"等打断词表示对对方的认同。需要将响应词按照时间点插入对话。被打断前的句子句末用逗号，而不是句号。
- 保证说话人角色统一，不要出现主持人介绍技术细节，或者引导主持人讨论主题等情况。
- 主持人根据嘉宾的回答，逐步增加对该领域的认知。但主持人不一定立刻能理解，也不一定理解地完全正确。主持人可以表达不理解或者提出一些常人可能会存在的疑问。这种情况下，嘉宾会进一步用更通俗的语言解释，或者针对性地解答常人常有的疑问或者误解。这种互动相比于永远正确的主持人和嘉宾更加真实，也更利于观众地理解。

### (二) 主题顺序安排
- 主持人会根据总结性文本，将主题排列，并保证主题间有逻辑关联，如从整体过渡到细节，从细节过渡到整体，从原因过渡到结果，从技术过渡到应用等。
- 主持人会引导对话节奏，按照总结性文本的主题顺序进行讨论。嘉宾不应该干扰主题过渡。

### (三) 知识速率
- 剧本中知识速率需要合理，不能短时间过快引入大量知识。知识不能突然增加，要逐渐引入，确保听众能够理解。
- 听众视角：充分考虑听众感受，从听众视角进行剧本创作。必须保证剧本不包含详细数学公式，而应该用通俗的语言介绍。确保剧本内容易懂，不要过于专业化。
- 无论是与主题相关的信息，还是离题的故事，都要按照你的知识进行充分地讨论，切忌简单地提一句而没有展开。要保证剧本足够真实，符合日常对话的逻辑，保证说话人间足够的尊重，不敷衍，不随意打断。

## 六、其他要求
### (一) 外语数字：
  1. 剧本将用于中文播客内容的录制。请保证大部分外语和数字转换为中文，以便于模型能正确识别读音。
  2. 请根据上下文，智慧地判断正确的读音。例如，“2021”如果表达年份，应当转换为“二零二一”。但如果表示数字，应当转换为“两千零二十一”。一些英文简称里常用数字代表英文单词，比如“a2b”代表“a to b”，“a4b”代表“a for b”，请保证不要简单转换为中文数字，而是根据上下文，将其翻译成合适的中文。
  3. 对于一些不常见的英文简写，如果根据上下文判断读音需要逐个字母阅读，则须保证每个字母间留有空格，如“AI”添加空格为“A I”，以避免模型误认为是一个单词。除非实体名字有常见的中文翻译，否则不要翻译实体名字。
### (二) 剧本长度
  1. 请控制"text"值的文本总长度不超过3000字符，且不超过60个发言，否则不合格。请选择技术细节，主题概念进行讨论。不要为了字数限制缩短每个话题讨论的深度，不要局限于总结文本，充分发挥你的知识。

INPUT: {BRIEF}  
 
再次强调：
说话人0是主持人, 说话人1是嘉宾。说话人和嘉宾没有姓名。剧本文本只使用逗号，句号和问号。禁止使用叹号。禁止使用省略号（'…'）、括号、引号（包括‘’“”'"）或波折号，否则视为不合格。请优先保证每个话题讨论的深度，不要局限于总结文本，利用你的知识，补充背景知识，举例说明等方式，让听众更好地理解。
请保证大部分外语和数字转换为中文，以便于模型能正确识别读音。在技术文档里，英文简称常用数字代表英文单词，比如“a2b”代表“a to b”，“a4b”代表“a for b”，请保证不要简单转换为中文数字，而是根据上下文，将其翻译成合适的中文。

OUTPUT:
'''
