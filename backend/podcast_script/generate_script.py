# 创建一个脚本生成工具
import argparse
import json
import os
import sys
from typing import Dict, Any, List

# 导入提示模板
from .en_llmprompt_gen_script import INPUT2BRIEF as EN_INPUT2BRIEF, BRIEF2SCRIPT as EN_BRIEF2SCRIPT
from .zh_llmprompt_gen_script import INPUT2BRIEF as ZH_INPUT2BRIEF, BRIEF2SCRIPT as ZH_BRIEF2SCRIPT
from llm.llm import LLMModel

def generate_brief(model_instance: LLMModel, input_text: str, language: str = "zh") -> str:
    """
    使用LLM生成文档摘要
    Args:
        input_text: 输入文档文本
        language: 语言，"en"或"zh"
        
    Returns:
        生成的摘要文本
    """
    print(f"[INFO] 使用{language}提示模板生成摘要...")
    # 获取对应语言的提示模板
    prompt_template = EN_INPUT2BRIEF if language == "en" else ZH_INPUT2BRIEF
    
    # 构造输入
    messages = [
        {"role": "system", "content": prompt_template},
        {"role": "user", "content": input_text}
    ]
    
    try:
        brief = model_instance.generate(messages, max_tokens=2000)
        return brief
    except Exception as e:
        print(f"[ERROR] 生成摘要失败: {e}")
        return f"摘要生成失败: {str(e)}"

def generate_script(model_instance: LLMModel, brief: str, language: str = "zh") -> List[Dict[str, str]]:
    """
    使用LLM根据摘要生成播客脚本
    Args:
        brief: 摘要文本
        language: 语言，"en"或"zh"
    Returns:
        JSON格式的对话脚本 (List of dicts with "role" and "text")
    """
    print(f"[INFO] 使用{language}提示模板生成脚本...")
    # 获取对应语言的提示模板
    prompt_template = EN_BRIEF2SCRIPT if language == "en" else ZH_BRIEF2SCRIPT
    
    # 构造输入
    # The prompt_template (BRIEF2SCRIPT) expects "INPUT: {BRIEF}"
    formatted_prompt = prompt_template.replace("{BRIEF}", brief)
    messages = [
        {"role": "user", "content": formatted_prompt}
    ]
    
    try:
        script_raw_json = model_instance.generate(messages, max_tokens=5000)

        # Clean and parse JSON from LLM response (might be in a code block)
        cleaned_json_str = script_raw_json.strip()
        if cleaned_json_str.startswith("```json"):
            cleaned_json_str = cleaned_json_str[len("```json"):].strip()
            if cleaned_json_str.endswith("```"):
                cleaned_json_str = cleaned_json_str[:-len("```")].strip()
        elif cleaned_json_str.startswith("```"): # Handle generic ``` ```
            cleaned_json_str = cleaned_json_str[len("```"):].strip()
            if cleaned_json_str.endswith("```"):
                cleaned_json_str = cleaned_json_str[:-len("```")].strip()
        
        script_json_from_llm = json.loads(cleaned_json_str)
        
        # Validate and transform from "speaker" (potentially in LLM output) to "role"
        processed_script = []
        if not isinstance(script_json_from_llm, list):
            raise ValueError("生成的脚本不是有效的JSON数组")
        for item in script_json_from_llm:
            if not isinstance(item, dict) or ("speaker" not in item and "role" not in item) or "text" not in item:
                raise ValueError("脚本项缺少 'speaker'/'role' 或 'text' 字段")
            
            # Standardize to "role" and ensure role is a string
            role_value = str(item.get("speaker", item.get("role")))
            processed_script.append({"role": role_value, "text": item["text"]})
            
        return processed_script
    except Exception as e:
        print(f"[ERROR] 生成脚本失败: {e}")
        # 返回默认脚本作为后备
        if language == "en":
            return [
                {"role": "0", "text": "Hello and welcome to our podcast!"},
                {"role": "1", "text": "Thanks for having me. I'm excited to be here."}
            ]
        else:
            return [
                {"role": "0", "text": "大家好，欢迎收听我们的播客！"},
                {"role": "1", "text": "谢谢邀请，我很高兴能来到这里。"}
            ]

def main():
    parser = argparse.ArgumentParser(description="生成播客脚本")
    parser.add_argument("input_file", help="输入文档文件路径")
    parser.add_argument("output_file", help="输出脚本文件路径")
    parser.add_argument("--language", "-l", choices=["en", "zh"], default="en", help="语言 (en或zh)")
    args = parser.parse_args()
    
    # 读取输入文件
    try:
        with open(args.input_file, "r", encoding="utf-8") as f:
            input_text = f.read()
    except Exception as e:
        print(f"[ERROR] 读取输入文件失败: {e}")
        sys.exit(1)
    
    # 获取LLM模型单例
    model_instance = LLMModel.get_instance()
    # 生成摘要
    brief = generate_brief(model_instance, input_text, args.language)
    # 生成脚本
    script = generate_script(model_instance, brief, args.language)
    # 保存脚本
    try:
        with open(args.output_file, "w", encoding="utf-8") as f:
            json.dump(script, f, ensure_ascii=False, indent=2)
        print(f"[INFO] 脚本已保存到 {args.output_file}")
    except Exception as e:
        print(f"[ERROR] 保存脚本失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()