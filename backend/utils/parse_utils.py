def parse_bool(val, default=False):
    if val is None:
        return default
    if isinstance(val, bool):
        return val
    return str(val).lower() in ['1', 'true', 'yes', 'on']

def parse_float(val, default=0.0):
    try:
        return float(val)
    except (TypeError, ValueError):
        return default

def parse_int(val, default=0):
    try:
        return int(val)
    except (TypeError, ValueError):
        return default