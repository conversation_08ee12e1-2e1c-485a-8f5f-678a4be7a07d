from backend.config import get_postgres_config
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from backend.db.models import Base

_engine = None
_SessionLocal = None

def get_engine_and_sessionmaker():
    """
    获取 SQLAlchemy engine 和 sessionmaker 工厂（单例模式）。
    首次调用时创建，后续直接返回同一实例。
    """
    global _engine, _SessionLocal
    if _engine is not None and _SessionLocal is not None:
        return _engine, _SessionLocal
    cfg = get_postgres_config()
    user = cfg.get("user")
    password = cfg.get("password")
    host = cfg.get("host")
    port = cfg.get("port")
    database = cfg.get("database")
    url = f"postgresql://{user}:{password}@{host}:{port}/{database}"
    _engine = create_engine(url, echo=False, future=True)
    _SessionLocal = sessionmaker(bind=_engine, autoflush=False, autocommit=False)
    return _engine, _SessionLocal


def init_db():
    """
    初始化数据库，自动创建所有表结构。
    """
    engine, _ = get_engine_and_sessionmaker()
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    init_db()