from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy import JSON

Base = declarative_base()

class PodcastScript(Base):
    __tablename__ = "podcast_script"

    id = Column(Integer, primary_key=True, autoincrement=True)
    original_text = Column(Text, nullable=False)
    summary_text = Column(Text, nullable=True)
    script_text = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<PodcastScript(id={self.id})>"

class PodcastAudio(Base):
    __tablename__ = "podcast_audio"

    id = Column(Integer, primary_key=True, autoincrement=True)
    script_text = Column(Text, nullable=False)
    voice_model_name = Column(String(128), nullable=False)
    audio_url = Column(String(512), nullable=False)
    edit_audio_url = Column(String(512), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return (
            f"<PodcastAudio(id={self.id}, "
            f"script_text={self.script_text!r}, "
            f"voice_model_name={self.voice_model_name!r}, "
            f"audio_url={self.audio_url!r}, "
            f"edit_audio_url={self.edit_audio_url!r}, "
            f"created_at={self.created_at})>"
        )
class TTSRecord(Base):
    __tablename__ = "tts_record"

    id = Column(Integer, primary_key=True, autoincrement=True)
    text = Column(Text, nullable=False)
    voice = Column(String(128), nullable=False)
    audio_url = Column(String(512), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return (
            f"<TTSRecord(id={self.id}, text={self.text!r}, voice={self.voice!r}, "
            f"audio_url={self.audio_url!r}, created_at={self.created_at})>"
        )


class AIVideoScript(Base):
    """
    AIVideo 脚本主表，所有相关字段合并存储
    - type: 视频类型
    - input: 主题
    - title: 标题
    - content: 视频文案内容
    - cap: 关键词
    - storyboard_list: 分镜描述列表（JSON）
    - image_list: 图片列表（JSON）
    - audio_list: 配音列表（JSON）
    - duration_list: 时间戳列表（JSON）
    - draft_url: 草稿URL
    - debug_url: 调试URL
    - created_at: 创建时间
    """
    __tablename__ = "aivideo_script"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键")
    type = Column(String(64), nullable=True, comment="视频类型")
    input = Column(String(128), nullable=False, comment="主题")
    title = Column(String(256), nullable=False, comment="标题")
    content = Column(Text, nullable=True, comment="视频文案内容")
    cap = Column(Text, nullable=True, comment="关键词")
    storyboard_list = Column(JSON, nullable=True, comment="分镜描述列表（JSON）")
    image_list = Column(JSON, nullable=True, comment="图片列表（JSON）")
    audio_list = Column(JSON, nullable=True, comment="配音列表（JSON）")
    duration_list = Column(JSON, nullable=True, comment="时间戳列表（JSON）")
    draft_url = Column(String(512), nullable=True, comment="草稿URL")
    debug_url = Column(String(512), nullable=True, comment="调试URL")
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")

    def __repr__(self):
        return f"<AIVideoScript(id={self.id}, title={self.title!r})>"