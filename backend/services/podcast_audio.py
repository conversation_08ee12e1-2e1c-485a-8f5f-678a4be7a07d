# 音频生成服务模块

import os
import json
import base64
from pydub import AudioSegment

from backend.db.db import get_engine_and_sessionmaker
from backend.db.models import PodcastAudio
from backend.services.minio_service import upload_audio_to_minio

from typing import Optional

def save_podcast_audio(
    script_text: str,
    voice_model_name: str,
    audio_url: str,
    edit_audio_url: Optional[str] = None
) -> int:
    """
    持久化播客音频，写入 PodcastAudio 表，返回主键 id。
    保证 script_text 为字符串，若为 dict/list 类型则自动序列化。
    edit_audio_url 可选，若传入则写入该字段，否则不写入。
    """
    import json
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        # 保证 script_text 为字符串
        if isinstance(script_text, (dict, list)):
            script_text = json.dumps(script_text, ensure_ascii=False)
        if edit_audio_url is not None:
            obj = PodcastAudio(
                script_text=script_text,
                voice_model_name=voice_model_name,
                audio_url=audio_url,
                edit_audio_url=edit_audio_url
            )
        else:
            obj = PodcastAudio(
                script_text=script_text,
                voice_model_name=voice_model_name,
                audio_url=audio_url
            )
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj.id
    finally:
        session.close()

def update_podcast_audio_edit_url(audio_id: int, edit_audio_url: str) -> None:
    """
    根据 id 更新 PodcastAudio 的 edit_audio_url 字段。
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        obj = session.query(PodcastAudio).filter(PodcastAudio.id == audio_id).first()
        if obj:
            obj.edit_audio_url = edit_audio_url
            session.commit()
    finally:
        session.close()

def parse_and_validate_dialogue(json_dialogue_str: str, language: str):
    """
    解析并校验输入的 JSON 字符串。

    参数:
        json_dialogue_str (str): 前端传入的 json_dialogue 字符串。
        language (str): 语言类型（如 "zh"、"en"），决定参考音频和文本。

    返回:
        list: 校验通过返回结构化对话（list）。
        dict: 校验失败返回 {"error": ...}。
    """
    try:
        json_dialogue = json.loads(json_dialogue_str)
    except json.JSONDecodeError:
        return {"error": "Invalid JSON format"}

    def validate_json(data):
        if not isinstance(data, list):
            return "JSON must be a list"
        cur_spk_should_be = 0
        for item in data:
            if 'role' not in item or 'text' not in item:
                return "Each item must have 'role' and 'text' fields"
            if item['role'] != str(cur_spk_should_be):
                return f"Role should be {cur_spk_should_be} in item {item}"
            cur_spk_should_be = 1 - cur_spk_should_be
        return None

    validation_error = validate_json(json_dialogue)
    if validation_error:
        return {"error": validation_error}
    return json_dialogue

def get_voice_for_role(role: str, language: str, voice_map: dict = None) -> str:
    """
    根据角色和语言返回 voice 名称，兼容嵌套和扁平 voice_map 结构。
    """
    print(f"[get_voice_for_role] role={role}, language={language}")
    if voice_map is None:
        from backend.config import get_voice_map
        voice_map = get_voice_map()
    print(f"[get_voice_for_role] voice_map type={type(voice_map)}, keys={list(voice_map.keys()) if hasattr(voice_map, 'keys') else voice_map}")
    voices = None
    if isinstance(voice_map.get(language), dict):
        voices = voice_map.get(language, {}).get(role)
    else:
        voices = voice_map.get(role)
    print(f"[get_voice_for_role] voices type={type(voices)}, value={voices}")
    if isinstance(voices, list) and voices:
        print(f"[get_voice_for_role] return={voices[0]}")
        return voices[0]
    elif isinstance(voices, str):
        print(f"[get_voice_for_role] return={voices}")
        return voices
    print(f"[get_voice_for_role] return=None")
    return None

def synthesize_and_save_to_file(text: str, voice: str, file_path: str) -> dict:
    """
    调用 TTS 合成文本并保存为音频文件。

    参数:
        text (str): 需要合成的文本内容。
        voice (str): 语音名称。
        file_path (str): 保存音频的目标文件路径。

    返回:
        dict: 成功时返回 {"success": True}，失败时返回 {"error": ...}。
    """
    from backend.services.azure_tts_service import azure_tts_synthesize
    try:
        tts_result = azure_tts_synthesize(text, voice)
        if "error" in tts_result:
            return {"error": tts_result["error"]}
        audio_b64 = tts_result.get("audio")
        audio_bytes = base64.b64decode(audio_b64)
        with open(file_path, "wb") as f:
            f.write(audio_bytes)
        return {"success": True}
    except Exception as e:
        return {"error": f"TTS 合成或写入文件失败: {str(e)}"}

def merge_audio_files(file_paths: list, output_path: str) -> dict:
    """
    合并所有音频文件为一个完整音频。

    参数:
        file_paths (list): 需合并的音频文件路径列表。
        output_path (str): 合并后输出文件路径。

    返回:
        dict: 成功时返回 {"success": True}，失败时返回 {"error": ...}。
    """
    try:
        if not file_paths:
            return {"error": "无有效音频片段"}
        audio_segments = []
        for idx, tmp_path in enumerate(file_paths):
            try:
                segment = AudioSegment.from_file(tmp_path, format="wav")
                audio_segments.append(segment)
            except Exception as e:
                return {"error": f"临时音频文件解码失败（第{idx+1}段）: {str(e)}"}
        combined = audio_segments[0]
        for seg in audio_segments[1:]:
            combined += seg
        combined.export(output_path, format="wav")
        return {"success": True}
    except Exception as e:
        return {"error": f"音频合并失败: {str(e)}"}

def cleanup_temp_files(file_paths: list):
    """
    删除指定临时文件。

    参数:
        file_paths (list): 需删除的文件路径列表。
    """
    for f in file_paths:
        try:
            if os.path.exists(f):
                os.remove(f)
        except Exception:
            pass

def generate_audio_service(
    json_dialogue_str: str,
    language: str = "zh",
    voice_map: dict = None,
    generate_edit_audio: bool = False,
    audio_edit_preset: str = None
) -> dict:
    """
    音频生成服务的业务入口函数（多角色 TTS 合成与拼接）。
    只负责调度各功能方法，不包含无关逻辑。
    支持自定义 voice_map。
    参数:
        generate_edit_audio (bool): 是否生成合成音频，默认 False。仅为 True 时才执行合成音频主流程。
        intro_path, outro_path, bgm_path, intro_volume, outro_volume, bgm_volume, bgm_loop, bgm_offset_ms: 合成音频相关参数。
    """
    import tempfile
    import os

    def build_voice_model_name(language, voice_map):
        voice_names = []
        for role in ["0", "1"]:
            vname = get_voice_for_role(role, language, voice_map)
            if vname:
                voice_names.append(f"角色{role}:{vname}")
        return ",".join(voice_names)

    def generate_and_upload_audio(parsed_dialogue, tmp_dir, json_dialogue_str, language, voice_map):
        tmp_files = []
        final_wav_path = os.path.join(tmp_dir, "final.wav")
        try:
            # TTS 生成片段
            for idx, item in enumerate(parsed_dialogue):
                role = str(item.get("role"))
                text = item.get("text", "")
                voice = get_voice_for_role(role, language, voice_map)
                if not voice:
                    return None, None, None, None, {
                        "status": "error",
                        "audio_url": None,
                        "edit_audio_url": None,
                        "error": f"未知角色: {role}，请检查 voice 配置"
                    }
                tmp_path = os.path.join(tmp_dir, f"role{role}_{idx}.wav")
                tts_result = synthesize_and_save_to_file(text, voice, tmp_path)
                if "error" in tts_result:
                    cleanup_temp_files(tmp_files + [tmp_path])
                    return None, None, None, None, {
                        "status": "error",
                        "audio_url": None,
                        "edit_audio_url": None,
                        "error": f"TTS 合成失败（第{idx+1}句，角色{role}）: {tts_result['error']}"
                    }
                tmp_files.append(tmp_path)
            # 合并片段
            merge_result = merge_audio_files(tmp_files, final_wav_path)
            if "error" in merge_result:
                cleanup_temp_files(tmp_files + [final_wav_path])
                return None, None, None, None, {
                    "status": "error",
                    "audio_url": None,
                    "edit_audio_url": None,
                    "error": f"音频合并失败: {merge_result['error']}"
                }
            # 上传主音频
            try:
                import uuid
                object_name = f"podcast_audio/{uuid.uuid4().hex}.wav"
                audio_url = upload_audio_to_minio(object_name=object_name, file_path=final_wav_path)
            except Exception as minio_exc:
                cleanup_temp_files(tmp_files + [final_wav_path])
                return None, None, None, None, {
                    "status": "error",
                    "audio_url": None,
                    "edit_audio_url": None,
                    "error": f"音频上传失败: {str(minio_exc)}"
                }
            # 组装 voice_model_name
            voice_model_name = build_voice_model_name(language, voice_map)
            # 数据库写入
            try:
                audio_id = save_podcast_audio(
                    script_text=json_dialogue_str,
                    voice_model_name=voice_model_name,
                    audio_url=audio_url
                )
            except Exception as db_exc:
                cleanup_temp_files(tmp_files + [final_wav_path])
                return None, None, None, None, {
                    "status": "error",
                    "audio_url": audio_url if 'audio_url' in locals() else None,
                    "edit_audio_url": None,
                    "error": f"数据库写入失败: {str(db_exc)}"
                }
            return tmp_files, final_wav_path, audio_url, audio_id, None
        except Exception as e:
            cleanup_temp_files(tmp_files + [final_wav_path])
            return None, None, None, None, {
                "status": "error",
                "audio_url": None,
                "edit_audio_url": None,
                "error": f"主音频生成异常: {str(e)}"
            }

    def generate_and_upload_edit_audio(final_wav_path, tmp_dir, json_dialogue_str, voice_model_name, audio_url, audio_id,
                                      intro_path, outro_path, bgm_path, intro_volume, outro_volume, bgm_volume, bgm_loop, bgm_offset_ms):
        from backend.services.podcast_edit import edit_audio_with_intro_outro_bgm_core
        edit_audio_url = None
        edit_audio_error = None
        edit_wav_path = os.path.join(tmp_dir, "edit_final.wav")
        try:
            edit_result = edit_audio_with_intro_outro_bgm_core(
                main_audio_path=final_wav_path,
                intro_path=intro_path,
                outro_path=outro_path,
                bgm_path=bgm_path,
                intro_volume=intro_volume,
                outro_volume=outro_volume,
                bgm_volume=bgm_volume,
                bgm_loop=bgm_loop,
                bgm_offset_ms=bgm_offset_ms
            )
            if isinstance(edit_result, dict) and "error" in edit_result:
                edit_audio_error = f"合成音频生成失败: {edit_result['error']}"
                return None, edit_audio_error
            final_audio, _ = edit_result
            try:
                final_audio.export(edit_wav_path, format="wav")
                import uuid
                object_name = f"edited_audio/{uuid.uuid4().hex}.wav"
                edit_audio_url = upload_audio_to_minio(object_name=object_name, file_path=edit_wav_path)
            except Exception as export_exc:
                edit_audio_error = f"合成音频导出或上传失败: {str(export_exc)}"
                edit_audio_url = None
            # 更新数据库 edit_audio_url
            if edit_audio_url and audio_id:
                try:
                    update_podcast_audio_edit_url(audio_id, edit_audio_url)
                except Exception as db_exc:
                    edit_audio_error = f"数据库写入 edit_audio_url 失败: {str(db_exc)}"
            return edit_audio_url, edit_audio_error
        finally:
            if os.path.exists(edit_wav_path):
                try:
                    os.remove(edit_wav_path)
                except Exception:
                    pass

    # 1. 解析和校验输入
    parsed = parse_and_validate_dialogue(json_dialogue_str, language)
    if isinstance(parsed, dict) and "error" in parsed:
        return {
            "status": "error",
            "audio_url": None,
            "edit_audio_url": None,
            "error": parsed.get("error", "输入解析校验失败")
        }

    tmp_dir = tempfile.mkdtemp(prefix="tts_tmp_")
    tmp_files, final_wav_path, audio_url, audio_id, main_error = generate_and_upload_audio(
        parsed, tmp_dir, json_dialogue_str, language, voice_map
    )

    if main_error:
        return main_error

    voice_model_name = build_voice_model_name(language, voice_map)

    edit_audio_url = None
    edit_audio_error = None

    try:
        if not generate_edit_audio:
            cleanup_temp_files(tmp_files + [final_wav_path])
            return {
                "status": "success",
                "id": audio_id,
                "audio_url": audio_url,
                "edit_audio_url": None,
                "error": None
            }

        # 生成 edit 音频
        from backend.services.podcast_edit import PRESET_AUDIO_EDIT_CONFIGS
        preset_key = audio_edit_preset or "default"
        preset = PRESET_AUDIO_EDIT_CONFIGS.get(preset_key, PRESET_AUDIO_EDIT_CONFIGS["default"])
        edit_audio_url, edit_audio_error = generate_and_upload_edit_audio(
            final_wav_path, tmp_dir, json_dialogue_str, voice_model_name, audio_url, audio_id,
            preset.get("intro_path"),
            preset.get("outro_path"),
            preset.get("bgm_path"),
            preset.get("intro_volume", 0.0),
            preset.get("outro_volume", 0.0),
            preset.get("bgm_volume", -10.0),
            preset.get("bgm_loop", True),
            preset.get("bgm_offset_ms", 0)
        )

        cleanup_temp_files(tmp_files + [final_wav_path])

        merged_error = edit_audio_error if edit_audio_error else None
        return {
            "status": "success",
            "id": audio_id,
            "audio_url": audio_url,
            "edit_audio_url": edit_audio_url if generate_edit_audio else None,
            "error": merged_error
        }
    except Exception as e:
        cleanup_temp_files(tmp_files + [final_wav_path])
        merged_error = str(e)
        if edit_audio_error:
            merged_error = f"{str(e)}；编辑音频异常：{edit_audio_error}"
        return {
            "status": "error",
            "id": audio_id if 'audio_id' in locals() else None,
            "audio_url": audio_url if 'audio_url' in locals() else None,
            "edit_audio_url": edit_audio_url if 'edit_audio_url' in locals() else None,
            "error": merged_error
        }