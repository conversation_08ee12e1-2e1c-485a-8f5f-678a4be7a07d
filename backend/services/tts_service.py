import base64

def generate_tts_audio(data: dict):
    """
    统一处理 TTS 生成请求的业务逻辑，包括参数校验、文件名生成、TTS合成、Min<PERSON>上传、数据库写入、响应组装。
    参数:
        data (dict): request.get_json() 的结果
    返回:
        dict: 响应内容
    """
    import uuid
    import os
    import tempfile
    from datetime import datetime
    from backend.db.models import TTSRecord
    from backend.db.db import get_engine_and_sessionmaker
    from backend.services.azure_tts_service import azure_tts_synthesize
    from backend.services.minio_service import upload_audio_to_minio

    text = data.get('text', '').strip()
    voice = data.get('voice', '').strip()
    if not text or not voice:
        return {"status": "error", "error": "text和voice参数不能为空"}, 400

    uuid_hex = uuid.uuid4().hex
    object_name = f"tts_audio/{uuid_hex}.wav"
    audio_url = None
    tmp_wav = None

    # 1. 调用TTS服务获取base64音频内容
    tts_result = azure_tts_synthesize(text, voice)
    if "error" in tts_result:
        return {"status": "error", "error": tts_result["error"]}, 500
    audio_b64 = tts_result.get("audio")
    if not audio_b64:
        return {"status": "error", "error": "TTS服务未返回音频内容"}, 500

    # 2. 写入临时wav文件
    try:
        audio_bytes = base64.b64decode(audio_b64)
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmpfile:
            tmp_wav = tmpfile.name
            tmpfile.write(audio_bytes)

        # 3. 上传到minio
        try:
            audio_url = upload_audio_to_minio(object_name=object_name, file_path=tmp_wav)
        except Exception as minio_exc:
            # 删除临时文件
            if tmp_wav and os.path.exists(tmp_wav):
                try:
                    os.remove(tmp_wav)
                except Exception:
                    pass
            return {"status": "error", "error": f"音频上传失败: {str(minio_exc)}"}, 500

    finally:
        # 4. 删除本地临时文件
        if tmp_wav and os.path.exists(tmp_wav):
            try:
                os.remove(tmp_wav)
            except Exception:
                pass

    # 5. 写入数据库
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        record = TTSRecord(
            text=text,
            voice=voice,
            audio_url=audio_url,
            created_at=datetime.utcnow()
        )
        session.add(record)
        session.commit()
        return {
            "status": "success",
            "audio_url": audio_url,
            "id": record.id
        }, 200
    finally:
        session.close()