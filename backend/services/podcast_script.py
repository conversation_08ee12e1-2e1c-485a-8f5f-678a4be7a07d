# 播客脚本生成服务模块

from backend.db.db import get_engine_and_sessionmaker
from backend.db.models import PodcastScript
import json

def save_podcast_script(original_text: str, summary_text: str, script_text: str) -> int:
    """
    持久化播客脚本，写入 PodcastScript 表，返回主键 id。
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        # 保证 script_text 为字符串
        if isinstance(script_text, (dict, list)):
            script_text = json.dumps(script_text, ensure_ascii=False)
        obj = PodcastScript(
            original_text=original_text,
            summary_text=summary_text,
            script_text=script_text
        )
        session.add(obj)
        session.commit()
        session.refresh(obj)
        return obj.id
    finally:
        session.close()

def generate_script_service(input_text: str, language: str = "zh") -> dict:
    try:
        if not input_text or not isinstance(input_text, str):
            return {"error": "input_text is required"}

        from podcast_script.generate_script import generate_brief, generate_script
        from llm.llm import LLMModel

        llm_instance = LLMModel.get_instance()

        # 第一步: 生成摘要
        brief = generate_brief(llm_instance, input_text, language)

        # 第二步: 生成脚本
        script = generate_script(llm_instance, brief, language)

        # 第三步: 写入数据库
        try:
            script_id = save_podcast_script(input_text, brief, script)
        except Exception as db_exc:
            return {
                "error": f"数据库写入失败: {str(db_exc)}",
                "brief": brief,
                "script": script
            }

        return {
            "status": "success",
            "id": script_id,
            "brief": brief,
            "script": script
        }
    except Exception as e:
        return {"error": str(e)}