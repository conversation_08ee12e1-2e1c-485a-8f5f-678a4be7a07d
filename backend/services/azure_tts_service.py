import os
import base64
import azure.cognitiveservices.speech as speechsdk
from backend.config import config

def azure_tts_synthesize(text: str, voice: str) -> dict:
    """
    使用 Azure 语音合成服务将文本转为音频。

    参数:
        text (str): 需要合成的文本内容。
        voice (str): 语音名称（如 'zh-CN-XiaoxiaoNeural'）。

    返回:
        dict: 成功时返回 {"audio": ...}（base64 编码），失败时返回 {"error": ...}。
    """
    # 通过统一 config 获取 Azure TTS 配置
    subscription_key = config['azure_tts']['AZURE_TTS_KEY']
    service_region = config['azure_tts']['AZURE_TTS_REGION']

    try:
        speech_config = speechsdk.SpeechConfig(subscription=subscription_key, region=service_region)
        speech_config.speech_synthesis_voice_name = voice
        synthesizer = speechsdk.SpeechSynthesizer(speech_config=speech_config, audio_config=None)
        result = synthesizer.speak_text_async(text).get()
        if result.reason == speechsdk.ResultReason.SynthesizingAudioCompleted:
            audio_data = result.audio_data
            audio_base64 = base64.b64encode(audio_data).decode("utf-8")
            return {"audio": audio_base64}
        else:
            return {"error": f"语音合成失败: {result.reason}"}
    except Exception as e:
        return {"error": f"Azure TTS 调用异常: {str(e)}"}