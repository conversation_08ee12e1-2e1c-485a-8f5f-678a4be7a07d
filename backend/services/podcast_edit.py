"""
音频编辑服务模块：分层实现音频拼接与混音（core）及服务层接口（service）。
- edit_audio_with_intro_outro_bgm_core：音频拼接与混音的内存处理。
依赖:
    pip install pydub
"""

import os
from typing import Optional, Tuple, Dict, Any, Union
from pydub import AudioSegment
from backend.services.minio_service import upload_audio_to_minio
import tempfile
import requests

# 预设音频编辑参数
PRESET_AUDIO_EDIT_CONFIGS = {
    "default": {
        "intro_path": "assets/audio/intro.mp3",
        "outro_path": "assets/audio/outro.mp3",
        "bgm_path": "assets/audio/bgm.mp3",
        "intro_volume": 0.0,
        "outro_volume": 0.0,
        "bgm_volume": -10.0,
        "bgm_loop": True,
        "bgm_offset_ms": 0
    }
}

def edit_audio_by_id(
    session,
    audio_id: int,
    audio_edit_preset: str = "default"
) -> dict:
    """
    根据 PodcastAudio id 和 audio_edit_preset 标识，合成 intro/outro/bgm 音频，上传 minio，更新 edit_audio_url 字段。

    参数:
        session: SQLAlchemy session
        audio_id (int): PodcastAudio 记录的唯一 id
        audio_edit_preset (str): 预设音频编辑参数标识，默认为 "default"

    返回:
        dict: { "success": True/False, "status": ..., "edit_audio_url": ..., "error": ... }
    """
    from backend.db.models import PodcastAudio
    import tempfile
    import uuid

    tmp_files = []
    edit_audio_url = None
    error = None

    try:
        # 1. 查找 PodcastAudio
        audio_obj = session.query(PodcastAudio).filter(PodcastAudio.id == audio_id).first()
        if not audio_obj:
            return {
                "success": False,
                "status": "not_found",
                "edit_audio_url": None,
                "error": f"未找到 id={audio_id} 的 PodcastAudio"
            }
                
        main_audio_path = audio_obj.audio_url
        
        def is_url(path: str) -> bool:
            return isinstance(path, str) and (path.startswith("http://") or path.startswith("https://"))
        
        # 判断主音频文件本地存在性
        if not is_url(main_audio_path) and not os.path.exists(main_audio_path):
            return {
                "success": False,
                "status": "audio_missing",
                "edit_audio_url": None,
                "error": "主音频文件不存在"
            }

        # 2. 查找 preset 配置
        preset = PRESET_AUDIO_EDIT_CONFIGS.get(audio_edit_preset, PRESET_AUDIO_EDIT_CONFIGS["default"])
        intro_path = preset.get("intro_path")
        outro_path = preset.get("outro_path")
        bgm_path = preset.get("bgm_path")
        intro_volume = preset.get("intro_volume", 0.0)
        outro_volume = preset.get("outro_volume", 0.0)
        bgm_volume = preset.get("bgm_volume", -10.0)
        bgm_loop = preset.get("bgm_loop", True)
        bgm_offset_ms = preset.get("bgm_offset_ms", 0)

        # 3. 合成音频
        result = edit_audio_with_intro_outro_bgm_core(
            main_audio_path=main_audio_path,
            intro_path=intro_path,
            outro_path=outro_path,
            bgm_path=bgm_path,
            intro_volume=intro_volume,
            outro_volume=outro_volume,
            bgm_volume=bgm_volume,
            bgm_loop=bgm_loop,
            bgm_offset_ms=bgm_offset_ms
        )
        if isinstance(result, dict) and "error" in result:
            error = f"音频合成失败: {result['error']}"
            return {
                "success": False,
                "status": "edit_failed",
                "edit_audio_url": None,
                "error": error
            }

        final_audio, _ = result

        # 4. 导出为临时 wav 文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmpfile:
            tmp_wav = tmpfile.name
            tmp_files.append(tmp_wav)
        try:
            final_audio.export(tmp_wav, format="wav")
        except Exception as export_exc:
            error = f"音频导出失败: {str(export_exc)}"
            return {
                "success": False,
                "status": "export_failed",
                "edit_audio_url": None,
                "error": error
            }

        # 5. 上传到 minio
        try:
            object_name = f"edited_audio/{uuid.uuid4().hex}.wav"
            edit_audio_url = upload_audio_to_minio(object_name=object_name, file_path=tmp_wav)
        except Exception as minio_exc:
            error = f"音频上传失败: {str(minio_exc)}"
            return {
                "success": False,
                "status": "upload_failed",
                "edit_audio_url": None,
                "error": error
            }

        # 6. 数据库更新
        try:
            audio_obj.edit_audio_url = edit_audio_url
            session.commit()
        except Exception as db_exc:
            error = f"数据库更新失败: {str(db_exc)}"
            return {
                "success": False,
                "status": "db_update_failed",
                "edit_audio_url": edit_audio_url,
                "error": error
            }

        return {
            "success": True,
            "status": "ok",
            "edit_audio_url": edit_audio_url,
            "error": None
        }
    except Exception as e:
        # 捕获未预期异常，保证结构兼容
        return {
            "success": False,
            "status": "exception",
            "edit_audio_url": edit_audio_url,
            "error": f"音频编辑异常: {str(e)}"
        }
    finally:
        # 7. 清理所有临时文件
        for f in tmp_files:
            try:
                if f and os.path.exists(f):
                    os.remove(f)
            except Exception:
                pass

def edit_audio_with_intro_outro_bgm_core(
    main_audio_path: str,
    intro_path: Optional[str] = None,
    outro_path: Optional[str] = None,
    bgm_path: Optional[str] = None,
    intro_volume: float = 0.0,
    outro_volume: float = 0.0,
    bgm_volume: float = -10.0,
    bgm_loop: bool = True,
    bgm_offset_ms: int = 0
) -> Union[Tuple[AudioSegment, Dict[str, Any]], Dict[str, str]]:
    """
    只做音频拼接与混音的内存处理，返回最终音频对象和信息字典。

    参数:
        main_audio_path (str): 主音频文件路径，必填。支持本地路径或 http/https 链接。
        intro_path (str): 片头音频路径，可选。
        outro_path (str): 片尾音频路径，可选。
        bgm_path (str): 背景音乐音频路径，可选。
        intro_volume (float): 片头音量（dB），默认 0.0。
        outro_volume (float): 片尾音量（dB），默认 0.0。
        bgm_volume (float): 背景音乐音量（dB），默认 -10.0。
        bgm_loop (bool): 背景音乐是否循环填满主音轨，默认 True。
        bgm_offset_ms (int): 背景音乐延迟起始时间（毫秒），默认 0。

    返回:
        (final_audio, info_dict): 成功时返回 (AudioSegment, info_dict)，
            info_dict 包含各段时长、最终时长等信息。
        dict: 失败时返回 {"error": ...}
    """
    def _is_url(path: str) -> bool:
        return isinstance(path, str) and (path.startswith("http://") or path.startswith("https://"))

    temp_main_file = None
    try:
        info = {}
        # 1. 若 main_audio_path 为 URL，先下载到本地临时文件
        if _is_url(main_audio_path):
            try:
                resp = requests.get(main_audio_path, stream=True, timeout=30)
                resp.raise_for_status()
                with tempfile.NamedTemporaryFile(suffix=".audio", delete=False) as tmpf:
                    for chunk in resp.iter_content(chunk_size=8192):
                        if chunk:
                            tmpf.write(chunk)
                    temp_main_file = tmpf.name
                main_audio_path_local = temp_main_file
            except Exception as e:
                return {"error": f"主音频下载失败: {str(e)}"}
        else:
            main_audio_path_local = main_audio_path

        # 2. 加载主音频
        try:
            main_audio = AudioSegment.from_file(main_audio_path_local)
            info["main_audio_ms"] = len(main_audio)
        except Exception as e:
            return {"error": f"主音频加载失败: {str(e)}"}

        # 3. 片头
        intro_audio = None
        if intro_path:
            try:
                intro_audio = AudioSegment.from_file(intro_path)
                intro_audio += intro_volume
                info["intro_audio_ms"] = len(intro_audio)
            except Exception as e:
                return {"error": f"片头加载或音量调整失败: {str(e)}"}

        # 4. 片尾
        outro_audio = None
        if outro_path:
            try:
                outro_audio = AudioSegment.from_file(outro_path)
                outro_audio += outro_volume
                info["outro_audio_ms"] = len(outro_audio)
            except Exception as e:
                return {"error": f"片尾加载或音量调整失败: {str(e)}"}

        # 5. 背景音乐
        bgm_audio = None
        if bgm_path:
            try:
                bgm_audio = AudioSegment.from_file(bgm_path)
                bgm_audio += bgm_volume
                main_len = len(main_audio)
                if bgm_loop and len(bgm_audio) > 0:
                    times = (main_len + len(bgm_audio) - 1) // len(bgm_audio)
                    bgm_audio = bgm_audio * times
                bgm_audio = bgm_audio[:main_len] if bgm_audio else None
                if bgm_audio and bgm_offset_ms > 0:
                    silence = AudioSegment.silent(duration=bgm_offset_ms)
                    bgm_audio = silence + bgm_audio
                    bgm_audio = bgm_audio[:main_len]
                if bgm_audio:
                    info["bgm_audio_ms"] = len(bgm_audio)
            except Exception as e:
                return {"error": f"背景音乐加载或处理失败: {str(e)}"}

        # 6. 合成最终音频
        try:
            if bgm_audio:
                if len(bgm_audio) < len(main_audio):
                    bgm_audio = bgm_audio + AudioSegment.silent(duration=(len(main_audio) - len(bgm_audio)))
                mixed = main_audio.overlay(bgm_audio)
            else:
                mixed = main_audio
            if intro_audio:
                mixed = intro_audio + mixed
            if outro_audio:
                mixed = mixed + outro_audio
            final_audio = mixed
            info["final_audio_ms"] = len(final_audio)
        except Exception as e:
            return {"error": f"音频拼接或混音失败: {str(e)}"}

        return final_audio, info
    except Exception as e:
        return {"error": f"音频编辑核心处理失败: {str(e)}"}
    finally:
        # 若有临时主音频文件，处理后及时清理
        if temp_main_file and os.path.exists(temp_main_file):
            try:
                os.remove(temp_main_file)
            except Exception:
                pass