import requests
import copy
import json
from sqlalchemy import desc
from sqlalchemy.orm.attributes import flag_modified
from typing import Optional
from backend.services.video_style_service import VideoStyleService
from backend.services.minio_service import batch_download_and_upload_to_minio
from backend.config import get_workflow_url
from backend.db.db import get_engine_and_sessionmaker
from backend.db.models import (
    AIVideoScript
)

class AIVideoServiceError(Exception):
    """自定义异常：AIVideoService 相关错误"""
    pass

def make_aivideo_resp_dict(script) -> dict:
    """
    工具函数：将 script ORM 对象转换为 resp_dict，字段覆盖原有 resp_dict 内容，兼容 draft_url/debug_url/draftURL/debugURL。
    """
    # draft_url/debug_url 兼容处理
    draft_url = getattr(script, "draft_url", None) or getattr(script, "draftURL", None)
    debug_url = getattr(script, "debug_url", None) or getattr(script, "debugURL", None)
    return {
        "id": script.id,
        "type": script.type,
        "input": script.input,
        "title": script.title,
        "content": script.content,
        "cap": script.cap,
        "storyboard_list": script.storyboard_list,
        "image_list": script.image_list,
        "audio_list": script.audio_list,
        "duration_list": script.duration_list,
        "draft_url": draft_url,
        "debug_url": debug_url,
        "created_at": script.created_at
    }


def upload_aivideo_assets_to_minio(data: dict) -> dict:
    """
    按最新 AIVideoScript 单表 JSON 结构，批量处理 image_list/audio_list，上传到 minio，替换为 minio 地址，返回新数据。
    :param data: 包含 image_list/audio_list 的原始数据
    :return: 替换为 minio 地址后的新数据
    :raises: AIVideoServiceError
    """
    try:
        new_data = copy.deepcopy(data)
        # 处理 image_list
        if "image_list" in new_data and isinstance(new_data["image_list"], list):
            minio_images = batch_download_and_upload_to_minio(new_data["image_list"])
            new_data["image_list"] = minio_images
        # 兼容部分前端可能传 imageList
        elif "imageList" in new_data and isinstance(new_data["imageList"], list):
            minio_images = batch_download_and_upload_to_minio(new_data["imageList"])
            new_data["image_list"] = minio_images
            new_data.pop("imageList", None)
        # 处理 audio_list
        if "audio_list" in new_data and isinstance(new_data["audio_list"], list):
            minio_audios = batch_download_and_upload_to_minio(new_data["audio_list"])
            new_data["audio_list"] = minio_audios
        # 兼容部分前端可能传 audioList
        elif "audioList" in new_data and isinstance(new_data["audioList"], list):
            minio_audios = batch_download_and_upload_to_minio(new_data["audioList"])
            new_data["audio_list"] = minio_audios
            new_data.pop("audioList", None)
        # 可扩展处理其他主表 JSON 素材字段（如有）

        return new_data
    except Exception as e:
        raise AIVideoServiceError(f"minio 批量上传失败: {str(e)}")

def save_aivideo_to_db(data: dict) -> int:
    """
    将处理后的数据存入数据库，结构与 models 一致，异常抛出。
    :param data: 处理后的 aivideo 数据
    :return: 新插入的 Script 主表 id
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        # 主表 AIVideoScript，所有多模态内容直接存 JSON 字段
        script = AIVideoScript(
            type=data.get("type", None),
            input=data.get("input", ""),
            title=data.get("title", data.get("name", "")),
            content=data.get("content", ""),
            cap=data.get("cap", ""),
            storyboard_list=data.get("storyboard_list", data.get("storyboardList", [])),
            image_list=data.get("image_list", data.get("imageList", [])),
            audio_list=data.get("audio_list", data.get("audioList", [])),
            duration_list=data.get("duration_list", data.get("durationList", [])),
            draft_url=data.get("draftURL", None),
            debug_url=data.get("debug_url", None)
        )
        session.add(script)
        session.commit()
        return script.id
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"保存 aivideo 到数据库失败: {str(e)}")
    finally:
        session.close()

def get_aivideo_list(page: int, page_size: int):
    """
    分页获取所有AI视频基本信息（新表结构）
    :param page: 页码（从1开始）
    :param page_size: 每页数量
    :return: {total, items: [{id, input, title, created_at}]}
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        query = session.query(AIVideoScript)
        total = query.count()
        items = (
            query.order_by(desc(AIVideoScript.id))
            .offset((page - 1) * page_size)
            .limit(page_size)
            .all()
        )
        result = []
        for s in items:
            result.append({
                "id": s.id,
                "input": s.input,
                "title": s.title,
                "created_at": s.created_at
            })
        return {"total": total, "items": result}
    except Exception as e:
        raise AIVideoServiceError(f"获取AI视频列表失败: {str(e)}")
    finally:
        session.close()

def get_aivideo_detail(script_id: int):
    """
    获取指定AI视频的全部详情（新表结构）
    :param script_id: AIVideoScript主键
    :return: dict，包含所有字段及多模态内容（JSON）
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=script_id).first()
        if not script:
            raise AIVideoServiceError("未找到指定ID的AI视频")
        detail = {
            "id": script.id,
            "type": script.type,
            "input": script.input,
            "title": script.title,
            "content": script.content,
            "cap": script.cap,
            "storyboard_list": script.storyboard_list,
            "image_list": script.image_list,
            "audio_list": script.audio_list,
            "duration_list": script.duration_list,
            "draft_url": script.draft_url,
            "debug_url": script.debug_url,
            "created_at": script.created_at
        }
        return detail
    except Exception as e:
        raise AIVideoServiceError(f"获取AI视频详情失败: {str(e)}")
    finally:
        session.close()

def generate_aivideo_step1(
    input_value: str,
    type: Optional[str] = "Jianbihua",
    left_top: str = "",
    right_top: str = ""
) -> dict:
    """
    AI视频“两步法”第一步：根据视频风格调用外部接口生成脚本，参数通过 body 传递，并保存input、content等信息到数据库（新表结构）。
    仅保存脚本相关内容，不生成draft_url、debug_url等最终视频信息。
    :param input_value: 前端传入的 input 字符串
    :param type: 可选，视频风格
    :param left_top: 可选，左上角内容，默认""
    :param right_top: 可选，右上角内容，默认""
    :return: 新表结构字段
    :raises: AIVideoServiceError
    """
    # 1. 调用外部接口
    base_url = get_workflow_url()
    url = VideoStyleService.get_api(type, "video_step1")
    if not url:
        raise AIVideoServiceError(f"未配置该视频风格的 video_step1 外部接口: {type}")
    url = url.format(base_url=base_url)
    prompts = VideoStyleService.get_prompts(type)
    payload = {
        "input": input_value,
        "script_prompt": prompts["script"] if prompts else None,
        "storyboard_prompt": prompts["storyboard"] if prompts else None,
        "left_top": left_top,
        "right_top": right_top
    }
    print(payload)
    try:
        resp = requests.post(url, json=payload, timeout=300)
        resp.raise_for_status()
        result = resp.json()
    except Exception as e:
        raise AIVideoServiceError(f"外部脚本生成接口请求失败: {str(e)}")

    # 2. 解析返回
    data = result[0] if isinstance(result, list) and result else result
    # 3. 保存到数据库
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = AIVideoScript(
            type=data.get("type", type),
            input=data.get("input", input_value),
            title=data.get("title", data.get("name", "")),
            content=data.get("content", ""),
            cap=data.get("cap", ""),
            storyboard_list=data.get("storyboard_list", data.get("storyboardList", [])),
            image_list=data.get("image_list", data.get("imageList", [])),
            audio_list=data.get("audio_list", data.get("audioList", [])),
            duration_list=data.get("duration_list", data.get("durationList", [])),
            draft_url=None,
            debug_url=None
        )
        session.add(script)
        session.commit()
        resp_dict = {
            "id": script.id,
            "type": script.type,
            "input": script.input,
            "title": script.title,
            "content": script.content,
            "cap": script.cap,
            "storyboard_list": script.storyboard_list,
            "image_list": script.image_list,
            "audio_list": script.audio_list,
            "duration_list": script.duration_list,
            "draft_url": script.draft_url,
            "debug_url": script.debug_url,
            "created_at": script.created_at
        }
        return resp_dict
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"保存AI视频脚本到数据库失败: {str(e)}")
    finally:
        session.close()

def generate_aivideo_step2(script_id: int) -> dict:
    """
    根据 script_id 查找 AIVideoScript，获取 title（name）、storyboard_list，
    调用外部接口获取 image_list，并仅更新 image_list 字段。
    :param script_id: AIVideoScript主键
    :return: 最新主表结构
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=script_id).first()
        if not script:
            raise AIVideoServiceError("未找到指定ID的AI视频脚本")
        # 调用外部接口获取 image_list
        base_url = get_workflow_url()
        type_val = script.type if script.type else "Jianbihua"
        url = VideoStyleService.get_api(type_val, "video_step2")
        if not url:
            raise AIVideoServiceError(f"未配置该视频风格的 video_step2 外部接口: {type_val}")
        url = url.format(base_url=base_url)
        payload = {
            "title": script.title,
            "storyboard_list": script.storyboard_list,
            "id": script.id
        }
        try:
            resp = requests.post(url, json=payload, timeout=900)
            resp.raise_for_status()
            result = resp.json()
        except Exception as e:
            raise AIVideoServiceError(f"外部接口请求失败: {str(e)}")
        # 只处理 image_list 字段
        data = result[0] if isinstance(result, list) and result else result
        # 兼容 image_list/imageList，批量上传至 minio 并替换
        image_list = data.get("image_list", data.get("imageList", []))
        minio_data = upload_aivideo_assets_to_minio({"image_list": image_list})
        script.image_list = minio_data.get("image_list", [])
        session.commit()
        # 返回最新主表结构
        return make_aivideo_resp_dict(script)
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"AI视频第二步入库失败: {str(e)}")
    finally:
        session.close()

def generate_aivideo_step3(script_id: int) -> dict:
    """
    根据 script_id 查找 AIVideoScript，获取 title（name）、storyboard_list，
    调用 step3 外部接口，参数为 title、storyboard_list，获取 audio_list、duration_list，
    更新主表 audio_list、duration_list 字段，返回最新主表结构。
    :param script_id: AIVideoScript主键
    :return: 最新主表结构
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=script_id).first()
        if not script:
            raise AIVideoServiceError("未找到指定ID的AI视频脚本")
        base_url = get_workflow_url()
        type_val = script.type if script.type else "Jianbihua"
        url = VideoStyleService.get_api(type_val, "video_step3")
        if not url:
            raise AIVideoServiceError(f"未配置该视频风格的 video_step3 外部接口: {type_val}")
        url = url.format(base_url=base_url)
        payload = {
            "title": script.title,
            "storyboard_list": script.storyboard_list,
            "id": script.id
        }
        try:
            resp = requests.post(url, json=payload, timeout=900)
            resp.raise_for_status()
            result = resp.json()
        except Exception as e:
            raise AIVideoServiceError(f"外部接口请求失败: {str(e)}")
        data = result[0] if isinstance(result, list) and result else result
        audio_list = data.get("audio_list", data.get("audioList", []))
        duration_list = data.get("duration_list", data.get("durationList", []))
        # 批量上传 audio_list 至 minio，兼容 audioList/audio_list 字段
        minio_data = upload_aivideo_assets_to_minio({"audio_list": audio_list})
        script.audio_list = minio_data.get("audio_list", [])
        script.duration_list = duration_list
        session.commit()
        return make_aivideo_resp_dict(script)
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"AI视频第三步入库失败: {str(e)}")
    finally:
        session.close()

def generate_aivideo_step4(script_id: int) -> dict:
    """
    根据 script_id 查找 AIVideoScript，获取 title、audio_list、duration_list、image_list、storyboard_list，
    调用 step4 外部接口，参数为上述五项，获取 draft_url，更新主表并返回最新主表结构。
    :param script_id: AIVideoScript主键
    :return: 最新主表结构
    :raises: AIVideoServiceError
    """
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=script_id).first()
        if not script:
            raise AIVideoServiceError("未找到指定ID的AI视频脚本")
        base_url = get_workflow_url()
        type_val = script.type if script.type else "Jianbihua"
        url = VideoStyleService.get_api(type_val, "video_step4")
        if not url:
            raise AIVideoServiceError(f"未配置该视频风格的 video_step4 外部接口: {type_val}")
        url = url.format(base_url=base_url)
        payload = {
            "title": script.title,
            "left_top": "",
            "right_top": "",
            "audio_list": json.dumps(script.audio_list),
            "duration_list": script.duration_list,
            "image_list": json.dumps(script.image_list),
            "storyboard_list": script.storyboard_list,
            "id": script.id
        }
        try:
            resp = requests.post(url, json=payload, timeout=900)
            resp.raise_for_status()
            result = resp.json()
        except Exception as e:
            raise AIVideoServiceError(f"外部接口请求失败: {str(e)}")
        data = result[0] if isinstance(result, list) and result else result
        draft_url = data.get("draft_url", data.get("draftURL", None))
        debug_url = data.get("debug_url", data.get("debugURL", None))
        script.draft_url = draft_url
        script.debug_url = debug_url
        session.commit()
        return make_aivideo_resp_dict(script)
    except Exception as e:
        session.rollback()
        raise AIVideoServiceError(f"AI视频第四步入库失败: {str(e)}")
    finally:
        session.close()

def replace_asset_service(script_id, asset_type, asset_index, content):
    """
    替换素材核心服务方法（新表结构），支持音频/图片，直接更新主表 JSON 字段。
    :param script_id: AIVideoScript主键
    :param asset_type: 'audio' 或 'image'
    :param asset_index: 素材在 JSON 列表中的索引
    :param content: 新内容（图片为新URL及prompt，音频为新URL及text）
    :return: (url, code, msg)
    code: 0=成功，4=未找到对象，5=webhook失败，6=minio失败，7=数据库失败，8=参数错误
    """
    if asset_type not in ("audio", "image"):
        return None, 8, "type 仅支持 audio 或 image"
    if not content:
        return None, 8, "content 不能为空"
    if asset_index < 0:
        return None, 8, "asset_index 不能小于 0"
    # 1. 查找主表
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=script_id).first()
        if not script:
            return None, 4, "未找到指定AI视频"
        # 2. webhook 调用
        base_url = get_workflow_url()
        type_val = script.type if script.type else "Jianbihua"
        url = VideoStyleService.get_api(type_val, asset_type)
        if not url:
            return None, 8, f"未配置该视频风格的 {asset_type} 外部接口: {type_val}"
        workflow_url = url.format(base_url=base_url)
        payload = {"input": content}
        try:
            resp = requests.post(workflow_url, json=payload, timeout=300)
            resp.raise_for_status()
            result = resp.json()
        except Exception as e:
            return None, 5, f"webhook 调用失败: {str(e)}"
        resource_url = None
        item = result[0] if isinstance(result, list) and result else result
        if isinstance(item, dict):
            # 兼容 image_url, imageUrl, link, audio_url, audioUrl 字段
            resource_url = (
                item.get("image_url")
                or item.get("imageUrl")
                or item.get("link")
                or item.get("audio_url")
                or item.get("audioUrl")
            )
        if not resource_url or not isinstance(resource_url, str):
            return None, 5, f"webhook 返回无效: {result}"
        # 3. minio 上传
        try:
            minio_urls = batch_download_and_upload_to_minio([resource_url])
            if not minio_urls or not minio_urls[0]:
                return None, 6, "资源存储失败，未获取到可访问 url"
            minio_url = minio_urls[0]
        except Exception as e:
            return None, 6, f"资源存储失败: {str(e)}"
        # 4. 数据库更新（直接更新主表 JSON 字段）
        if asset_type == "audio":
            # 处理 JSON 字段，支持字符串和对象两种情况
            audio_list = script.audio_list
            if isinstance(audio_list, str):
                audio_list = json.loads(audio_list) if audio_list else []
            elif audio_list is None:
                audio_list = []

            duration_list = script.duration_list
            if isinstance(duration_list, str):
                duration_list = json.loads(duration_list) if duration_list else []
            elif duration_list is None:
                duration_list = []

            if asset_index >= len(audio_list):
                return None, 4, "未找到指定音频素材"
            audio_list[asset_index] = minio_url
            script.audio_list = audio_list
            flag_modified(script, 'audio_list')

            # 同步更新 duration_list
            if asset_index < len(duration_list):
                new_duration = item.get("duration")
                if new_duration is not None:
                    duration_list[asset_index] = new_duration
                script.duration_list = duration_list
                flag_modified(script, 'duration_list')

            # 同步 storyboard_list[idx]['cap'] 字段
            storyboard_list = script.storyboard_list
            if isinstance(storyboard_list, str):
                storyboard_list = json.loads(storyboard_list) if storyboard_list else []
            elif storyboard_list is None:
                storyboard_list = []

            if asset_index < len(storyboard_list) and isinstance(storyboard_list[asset_index], dict):
                storyboard_list[asset_index]['cap'] = content
                script.storyboard_list = storyboard_list
                flag_modified(script, 'storyboard_list')
        else:
            # 处理 JSON 字段，支持字符串和对象两种情况
            image_list = script.image_list
            if isinstance(image_list, str):
                image_list = json.loads(image_list) if image_list else []
            elif image_list is None:
                image_list = []

            if asset_index >= len(image_list):
                return None, 4, "未找到指定图片素材"
            image_list[asset_index] = minio_url
            script.image_list = image_list
            flag_modified(script, 'image_list')

            # 同步 storyboard_list[idx]['desc_prompt'] 字段
            storyboard_list = script.storyboard_list
            if isinstance(storyboard_list, str):
                storyboard_list = json.loads(storyboard_list) if storyboard_list else []
            elif storyboard_list is None:
                storyboard_list = []

            if asset_index < len(storyboard_list) and isinstance(storyboard_list[asset_index], dict):
                storyboard_list[asset_index]['desc_prompt'] = content
                script.storyboard_list = storyboard_list
                flag_modified(script, 'storyboard_list')
        session.commit()
        return minio_url, 0, "success"
    except Exception as e:
        session.rollback()
        return None, 7, f"数据库更新失败: {str(e)}"
    finally:
        session.close()

def update_aivideo(id: int, title: str, content: str, user_id: Optional[int] = None) -> dict:
    """
    PATCH /api/aivideo/update
    更新指定视频的标题和文案，参数：id, title, content
    参数校验、视频存在性校验、权限校验（如有用户系统）、异常处理（404/403/500）
    返回结构严格遵循接口设计（status, data 或 status, message）
    """
    # 参数校验
    if not id or not title or not content:
        return {"status": "fail", "message": "id、title、content为必填参数"}
    _, SessionLocal = get_engine_and_sessionmaker()
    session = SessionLocal()
    try:
        script = session.query(AIVideoScript).filter_by(id=id).first()
        if not script:
            return {"status": "fail", "message": "未找到指定ID的视频（404）"}
        # 权限校验（如有用户系统，可根据 script.user_id 与 user_id 比较）
        # if user_id is not None and hasattr(script, "user_id") and script.user_id != user_id:
        #     return {"status": "fail", "message": "无权限操作该视频（403）"}
        # 更新字段
        if title and title.strip():
            script.title = title
        if content and content.strip():
            script.content = content
        session.commit()
        return {"status": "success", "data": make_aivideo_resp_dict(script)}
    except Exception as e:
        session.rollback()
        return {"status": "fail", "message": f"服务器内部错误（500）：{str(e)}"}
    finally:
        session.close()