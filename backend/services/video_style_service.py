class VideoStyleService:
    """
    统一视频风格服务，集中管理所有视频风格及其接口映射和提示词模板。
    """

    # 风格信息，首次访问时自动合并 prompts
    _styles = None

    # 提示词缓存，首次访问时加载 JSON 文件
    _prompts_cache = None
    _styles_base = {
        "Jianbihua": {
            "type": "Jianbihua",
            "name": "简笔画（思维模型）",
        },
        "Zhiyuxi_Chahua": {
            "type": "Zhiyuxi_Chahua",
            "name": "治愈系插画",
            "script_path": "assets/Zhiyuxi_Chahua_script.txt",
            "storyboard_path": "assets/Zhiyuxi_Chahua_storyboard.txt",
        },
        # 可继续添加其他风格
    }

    @classmethod
    def _load_prompts(cls, style_conf):
        """
        按风格配置加载 script/storyboard 的文本文件，返回结构化 dict
        :param style_conf: 风格配置 dict
        :return: dict 或 None
        """
        import os
        prompts = {}
        script_path = style_conf.get("script_path")
        storyboard_path = style_conf.get("storyboard_path")

        def is_txt_file(path):
            return path and path.lower().endswith('.txt')

        # 只要是 txt 文件，直接读取文本内容为字符串
        if script_path:
            abs_script_path = os.path.join(os.path.dirname(__file__), "../", script_path)
            if is_txt_file(script_path):
                try:
                    with open(abs_script_path, "r", encoding="utf-8") as f:
                        prompts["script"] = f.read()
                except Exception:
                    prompts["script"] = None
            else:
                try:
                    import yaml
                except ImportError:
                    yaml = None
                if yaml:
                    try:
                        with open(abs_script_path, "r", encoding="utf-8") as f:
                            prompts["script"] = yaml.safe_load(f)
                    except Exception:
                        prompts["script"] = None
                else:
                    prompts["script"] = None

        if storyboard_path:
            abs_storyboard_path = os.path.join(os.path.dirname(__file__), "../", storyboard_path)
            if is_txt_file(storyboard_path):
                try:
                    with open(abs_storyboard_path, "r", encoding="utf-8") as f:
                        prompts["storyboard"] = f.read()
                except Exception:
                    prompts["storyboard"] = None
            else:
                try:
                    import yaml
                except ImportError:
                    yaml = None
                if yaml:
                    try:
                        with open(abs_storyboard_path, "r", encoding="utf-8") as f:
                            prompts["storyboard"] = yaml.safe_load(f)
                    except Exception:
                        prompts["storyboard"] = None
                else:
                    prompts["storyboard"] = None

        return prompts if prompts else None

    @classmethod
    def _init_styles(cls):
        """
        初始化 _styles，合并 prompts 信息（按风格配置加载 YAML 文本）
        """
        if cls._styles is None:
            styles = {}
            for k, v in cls._styles_base.items():
                style = v.copy()
                # 仅对有路径的风格加载 prompts
                if "script_path" in style or "storyboard_path" in style:
                    style["prompts"] = cls._load_prompts(style)
                else:
                    style["prompts"] = None
                styles[k] = style
            cls._styles = styles

    @classmethod
    def reload_prompts(cls):
        """
        热更新 prompts，重新合并 YAML 文本
        """
        styles = {}
        for k, v in cls._styles_base.items():
            style = v.copy()
            if "script_path" in style or "storyboard_path" in style:
                style["prompts"] = cls._load_prompts(style)
            else:
                style["prompts"] = None
            styles[k] = style
        cls._styles = styles

    # 统一 API 路径模板
    _api_templates = {
        "video": "{base_url}/aivideo/common/generate/video",
        "video_step1": "{base_url}/aivideo/common/generate/video_step1",
        "video_step2": "{base_url}/aivideo/common/generate/video_step2",
        "video_step3": "{base_url}/aivideo/common/generate/video_step3",
        "video_step4": "{base_url}/aivideo/common/generate/video_step4",
        "image": "{base_url}/aivideo/common/generate/single_image",
        "audio": "{base_url}/aivideo/common/generate/single_audio",
    }

    @classmethod
    def get_all_styles(cls):
        """
        获取所有风格的基本信息（type、name）
        :return: List[Dict]
        """
        cls._init_styles()
        return [
            {"type": style["type"], "name": style["name"]}
            for style in cls._styles.values()
        ]

    @classmethod
    def get_style(cls, type_):
        """
        获取指定风格的详细信息（type、name、api、prompts）
        :param type_: 风格标识
        :return: Dict 或 None
        """
        cls._init_styles()
        return cls._styles.get(type_)

    @classmethod
    def get_api(cls, type_, api_name):
        """
        根据风格 type 和接口名称 api_name 动态拼接 API 路径，仅返回 url 字符串或 None
        :param type_: 风格标识
        :param api_name: 接口名称（如 video、video_step1、image、audio 等）
        :return: 路径模板字符串或 None
        """
        cls._init_styles()
        if api_name in cls._api_templates and cls._styles.get(type_):
            return cls._api_templates[api_name]
        return None

    @classmethod
    def get_prompts(cls, type_):
        """
        获取指定风格的两个提示词模板（type_ 不存在则返回 None）
        :param type_: 风格标识
        :return: Dict 或 None
        """
        cls._init_styles()
        style = cls._styles.get(type_)
        if style:
            prompts = style.get("prompts", None)
            return prompts
        return None
