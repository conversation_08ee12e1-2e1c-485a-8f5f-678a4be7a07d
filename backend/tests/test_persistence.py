import os
import pytest

from backend.services.podcast_script import save_podcast_script
from backend.services.podcast_audio import upload_audio_to_minio, save_podcast_audio
from backend.config import config, get_postgres_config, get_minio_config

# 使用如下命令运行单个测试用例：
# PYTHONPATH=. pytest backend/tests/test_persistence.py -k test_save_podcast_script

# 测试音频文件路径（需确保存在）
TEST_AUDIO_PATH = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "../../local_inference/role_audio/en_prompt0.wav")
)
TEST_BUCKET = get_minio_config().get("bucket")
TEST_OBJECT_NAME = "test_upload/en_prompt0.wav"
TEST_VOICE_MODEL = "en-US-JennyNeural"

@pytest.mark.integration
def test_save_podcast_script():
    original = "This is the original podcast text."
    summary = "This is the summary."
    script = "This is the script content."
    script_id = save_podcast_script(original, summary, script)
    assert isinstance(script_id, int) and script_id > 0
@pytest.mark.integration
def test_save_podcast_script_dict_list():
    original = "dict/list test"
    summary = "summary"
    # dict 类型
    script_dict = {"a": 1, "b": 2}
    # list 类型
    script_list = ["x", "y", 3]
    import pytest
    # dict 情况
    with pytest.raises(Exception):
        save_podcast_script(original, summary, script_dict)
    # list 情况
    with pytest.raises(Exception):
        save_podcast_script(original, summary, script_list)

@pytest.mark.integration
def test_upload_audio_to_minio():
    url = upload_audio_to_minio(
        bucket=TEST_BUCKET,
        object_name=TEST_OBJECT_NAME,
        file_path=TEST_AUDIO_PATH
    )
    assert isinstance(url, str) and url.startswith("http")

@pytest.mark.integration
def test_save_podcast_audio():
    # 直接传脚本内容
    script_text = "audio test script"
    # 上传音频，获取 url
    url = upload_audio_to_minio(
        bucket=TEST_BUCKET,
        object_name="test_upload/audio_test.wav",
        file_path=TEST_AUDIO_PATH
    )
    audio_id = save_podcast_audio(script_text, TEST_VOICE_MODEL, url)
    assert isinstance(audio_id, int) and audio_id > 0

@pytest.mark.integration
def test_config_load():
    # 直接断言 config 字典和关键配置项
    assert isinstance(config, dict)
    pg = get_postgres_config()
    minio = get_minio_config()
    assert "host" in pg and "user" in pg
    assert "endpoint" in minio and "access_key" in minio