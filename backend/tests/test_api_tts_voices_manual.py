import requests

BASE_URL = "http://localhost:5008/api/tts/voices"

def print_case(title, params, resp):
    print(f"用例：{title}")
    print(f"请求方式: GET")
    print(f"请求参数: {params}")
    print(f"响应内容: {resp.status_code} {resp.json()}")
    print("-" * 40)

def main():
    # 用例1：不传 lang，预期 voices 为中文
    resp1 = requests.get(BASE_URL)
    print_case("不传 lang，返回 voices 为中文（zh）", {}, resp1)

    # 用例2：传入有效 lang=en
    resp2 = requests.get(BASE_URL, params={"lang": "en"})
    print_case("传入有效 lang（en），返回对应 voices", {"lang": "en"}, resp2)

    # 用例3：传入无效 lang=xx
    resp3 = requests.get(BASE_URL, params={"lang": "xx"})
    print_case("传入无效 lang（xx），返回空数组", {"lang": "xx"}, resp3)

if __name__ == "__main__":
    main()