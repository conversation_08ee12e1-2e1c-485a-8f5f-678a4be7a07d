from backend.services.video_style_service import VideoStyleService

def test_zhiyuxi_chahua_prompts():
    import os

    # 获取 prompts
    result = VideoStyleService.get_prompts("Zhiyuxi_Chahua")
    assert result is not None, "get_prompts 返回 None"

    # 读取原始 txt 文件内容
    script_path = os.path.join(os.path.dirname(__file__), "../assets/Zhiyuxi_Chahua_script.txt")
    storyboard_path = os.path.join(os.path.dirname(__file__), "../assets/Zhiyuxi_Chahua_storyboard.txt")
    with open(script_path, "r", encoding="utf-8") as f:
        script_txt = f.read()
    with open(storyboard_path, "r", encoding="utf-8") as f:
        storyboard_txt = f.read()

    # 断言内容完全一致
    assert result["script"] == script_txt, "script_prompt 与 txt 文件内容不一致"
    assert result["storyboard"] == storyboard_txt, "storyboard_prompt 与 txt 文件内容不一致"

if __name__ == "__main__":
    test_zhiyuxi_chahua_prompts()
    print("验证通过：get_prompts 返回内容与 txt 文件完全一致")