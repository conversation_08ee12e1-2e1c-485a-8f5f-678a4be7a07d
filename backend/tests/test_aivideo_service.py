import pytest
from unittest.mock import patch, MagicMock
from backend.services.aivideo_service import generate_aivideo_step2, AIVideoServiceError

@pytest.fixture
def mock_script():
    class Script:
        id = 1
        type = "Jianbihua"
        title = "测试标题"
        storyboard_list = [{"desc": "分镜1"}, {"desc": "分镜2"}]
        image_list = []
        content = "内容"
        cap = "字幕"
        audio_list = []
        duration_list = []
        draft_url = None
        debug_url = None
        input = "输入"
        created_at = "2025-08-09"
    return Script()

def test_generate_aivideo_step2_success(mock_script):
    # mock session/query
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_session.commit.return_value = None
    # mock get_engine_and_sessionmaker
    mock_engine = None
    mock_SessionLocal = MagicMock(return_value=mock_session)
    # mock VideoStyleService.get_api
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(mock_engine, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step2"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"), \
         patch("backend.services.aivideo_service.requests.post") as mock_post:
        # mock 外部接口返回
        mock_post.return_value.json.return_value = [{"image_list": ["img1.png", "img2.png"]}]
        mock_post.return_value.raise_for_status.return_value = None
        result = generate_aivideo_step2(1)
        assert result["image_list"] == ["img1.png", "img2.png"]
        assert result["id"] == 1
        assert result["title"] == "测试标题"

def test_generate_aivideo_step2_script_not_found():
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = None
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step2"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"):
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step2(999)
        assert "未找到指定ID的AI视频脚本" in str(excinfo.value)

def test_generate_aivideo_step2_api_not_configured(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value=None), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"):
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step2(1)
        assert "未配置该视频风格的 video_step2 外部接口" in str(excinfo.value)

def test_generate_aivideo_step2_external_api_error(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step2"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"), \
         patch("backend.services.aivideo_service.requests.post") as mock_post:
        mock_post.side_effect = Exception("接口异常")
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step2(1)
        assert "外部接口请求失败" in str(excinfo.value)
from backend.services.aivideo_service import generate_aivideo_step3

def test_generate_aivideo_step3_success(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_session.commit.return_value = None
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step3"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"), \
         patch("backend.services.aivideo_service.requests.post") as mock_post:
        mock_post.return_value.json.return_value = [{
            "audio_list": ["audio1.mp3", "audio2.mp3"],
            "duration_list": [1.23, 4.56]
        }]
        mock_post.return_value.raise_for_status.return_value = None
        result = generate_aivideo_step3(1)
        assert result["audio_list"] == ["audio1.mp3", "audio2.mp3"]
        assert result["duration_list"] == [1.23, 4.56]
        assert result["id"] == 1
        assert result["title"] == "测试标题"

def test_generate_aivideo_step3_script_not_found():
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = None
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step3"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"):
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step3(999)
        assert "未找到指定ID的AI视频脚本" in str(excinfo.value)

def test_generate_aivideo_step3_api_not_configured(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value=None), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"):
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step3(1)
        assert "未配置该视频风格的 video_step3 外部接口" in str(excinfo.value)

def test_generate_aivideo_step3_external_api_error(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step3"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"), \
         patch("backend.services.aivideo_service.requests.post") as mock_post:
        mock_post.side_effect = Exception("接口异常")
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step3(1)
        assert "外部接口请求失败" in str(excinfo.value)
from backend.services.aivideo_service import generate_aivideo_step4

def test_generate_aivideo_step4_success(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_session.commit.return_value = None
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step4"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"), \
         patch("backend.services.aivideo_service.requests.post") as mock_post:
        mock_post.return_value.json.return_value = [{"draft_url": "http://draft.url/video.mp4"}]
        mock_post.return_value.raise_for_status.return_value = None
        result = generate_aivideo_step4(1)
        assert result["draft_url"] == "http://draft.url/video.mp4"
        assert result["id"] == 1
        assert result["title"] == "测试标题"

def test_generate_aivideo_step4_script_not_found():
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = None
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step4"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"):
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step4(999)
        assert "未找到指定ID的AI视频脚本" in str(excinfo.value)

def test_generate_aivideo_step4_api_not_configured(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value=None), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"):
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step4(1)
        assert "未配置该视频风格的 video_step4 外部接口" in str(excinfo.value)

def test_generate_aivideo_step4_external_api_error(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step4"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"), \
         patch("backend.services.aivideo_service.requests.post") as mock_post:
        mock_post.side_effect = Exception("接口异常")
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step4(1)
        assert "外部接口请求失败" in str(excinfo.value)

def test_generate_aivideo_step4_db_commit_error(mock_script):
    mock_session = MagicMock()
    mock_session.query().filter_by().first.return_value = mock_script
    mock_session.commit.side_effect = Exception("数据库异常")
    mock_SessionLocal = MagicMock(return_value=mock_session)
    with patch("backend.services.aivideo_service.get_engine_and_sessionmaker", return_value=(None, mock_SessionLocal)), \
         patch("backend.services.aivideo_service.VideoStyleService.get_api", return_value="http://fake/api/video_step4"), \
         patch("backend.services.aivideo_service.get_workflow_url", return_value="http://fake"), \
         patch("backend.services.aivideo_service.requests.post") as mock_post:
        mock_post.return_value.json.return_value = [{"draft_url": "http://draft.url/video.mp4"}]
        mock_post.return_value.raise_for_status.return_value = None
        with pytest.raises(AIVideoServiceError) as excinfo:
            generate_aivideo_step4(1)
        assert "AI视频第四步入库失败" in str(excinfo.value)