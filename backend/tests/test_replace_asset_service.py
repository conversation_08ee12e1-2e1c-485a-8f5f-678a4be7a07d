import pytest
from unittest.mock import patch, MagicMock
from backend.services import aivideo_service
from backend.db.models import (
    AIVideoScriptImage, AIVideoScriptImagePrompt,
    AIVideoMaterialItem
)
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from backend.db import models as db_models
from backend.db.db import Base
import types

@pytest.fixture(scope="function")
def db_session():
    # 使用内存数据库，保证测试独立
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    Session = scoped_session(sessionmaker(bind=engine))
    session = Session()
    yield session
    session.close()
    Session.remove()
    engine.dispose()

def prepare_image_asset(session, prompt="old prompt"):
    # 创建图片及 prompt
    script_id = 1
    img = AIVideoScriptImage(id=1, script_id=script_id, url="old_url")
    prompt_obj = AIVideoScriptImagePrompt(id=1, script_id=script_id, prompt=prompt)
    session.add(img)
    session.add(prompt_obj)
    session.commit()
    return img, prompt_obj

def prepare_audio_asset(session, text="old text"):
    # 创建音频素材
    material_id = 1
    item = AIVideoMaterialItem(id=1, material_id=material_id, text=text, audio_url="old_audio_url")
    session.add(item)
    session.commit()
    return item

@patch("backend.services.aivideo_service.batch_download_and_upload_to_minio")
@patch("backend.services.aivideo_service.requests.post")
def test_replace_image_prompt_update(mock_post, mock_minio, db_session, monkeypatch):
    # mock webhook 返回
    mock_post.return_value = MagicMock(status_code=200, json=lambda: {"imageUrl": "new_url"})
    mock_minio.return_value = ["minio_url"]
    img, prompt_obj = prepare_image_asset(db_session, prompt="old prompt")

    # patch get_engine_and_sessionmaker 返回当前 session
    monkeypatch.setattr(aivideo_service, "get_engine_and_sessionmaker", lambda: (None, lambda: lambda: db_session))

    # 执行替换，content 作为新 prompt
    url, code, msg = aivideo_service.replace_asset_service(
        asset_id=img.id, asset_type="image", content="new prompt"
    )
    db_session.refresh(prompt_obj)
    assert code == 0
    assert url == "minio_url"
    assert prompt_obj.prompt == "new prompt"

@patch("backend.services.aivideo_service.batch_download_and_upload_to_minio")
@patch("backend.services.aivideo_service.requests.post")
def test_replace_audio_text_update(mock_post, mock_minio, db_session, monkeypatch):
    mock_post.return_value = MagicMock(status_code=200, json=lambda: {"audioUrl": "new_audio_url"})
    mock_minio.return_value = ["minio_audio_url"]
    item = prepare_audio_asset(db_session, text="old text")
    monkeypatch.setattr(aivideo_service, "get_engine_and_sessionmaker", lambda: (None, lambda: lambda: db_session))

    # content 作为新 text
    url, code, msg = aivideo_service.replace_asset_service(
        asset_id=item.id, asset_type="audio", content="new text"
    )
    db_session.refresh(item)
    assert code == 0
    assert url == "minio_audio_url"
    assert item.text == "new text"

@patch("backend.services.aivideo_service.batch_download_and_upload_to_minio")
@patch("backend.services.aivideo_service.requests.post")
def test_replace_no_new_content(mock_post, mock_minio, db_session, monkeypatch):
    # content 与原内容一致，字段应保持不变
    mock_post.return_value = MagicMock(status_code=200, json=lambda: {"audioUrl": "new_audio_url"})
    mock_minio.return_value = ["minio_audio_url"]
    item = prepare_audio_asset(db_session, text="keep text")
    monkeypatch.setattr(aivideo_service, "get_engine_and_sessionmaker", lambda: (None, lambda: lambda: db_session))

    url, code, msg = aivideo_service.replace_asset_service(
        asset_id=item.id, asset_type="audio", content="keep text"
    )
    db_session.refresh(item)
    assert code == 0
    assert url == "minio_audio_url"
    assert item.text == "keep text"

@patch("backend.services.aivideo_service.batch_download_and_upload_to_minio")
@patch("backend.services.aivideo_service.requests.post")
def test_replace_idempotent(mock_post, mock_minio, db_session, monkeypatch):
    # 多次传相同内容，字段不重复更新
    mock_post.return_value = MagicMock(status_code=200, json=lambda: {"audioUrl": "new_audio_url"})
    mock_minio.return_value = ["minio_audio_url"]
    item = prepare_audio_asset(db_session, text="same text")
    monkeypatch.setattr(aivideo_service, "get_engine_and_sessionmaker", lambda: (None, lambda: lambda: db_session))

    # 第一次
    url1, code1, msg1 = aivideo_service.replace_asset_service(
        asset_id=item.id, asset_type="audio", content="same text"
    )
    db_session.refresh(item)
    # 第二次
    url2, code2, msg2 = aivideo_service.replace_asset_service(
        asset_id=item.id, asset_type="audio", content="same text"
    )
    db_session.refresh(item)
    assert code1 == 0 and code2 == 0
    assert url1 == url2 == "minio_audio_url"
    assert item.text == "same text"