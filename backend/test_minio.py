#!/usr/bin/env python3
"""
MinIO 连接测试脚本
用于诊断 MinIO 配置和连接问题
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backend.config import get_minio_config
from backend.services.minio_service import upload_audio_to_minio

def test_minio_config():
    """测试 MinIO 配置"""
    print("=== MinIO 配置测试 ===")
    config = get_minio_config()
    print(f"配置内容: {config}")
    
    required_keys = ['endpoint', 'access_key', 'secret_key', 'bucket']
    for key in required_keys:
        if key in config and config[key]:
            print(f"✓ {key}: {config[key][:10]}..." if key in ['access_key', 'secret_key'] else f"✓ {key}: {config[key]}")
        else:
            print(f"✗ {key}: 缺失或为空")
            return False
    return True

def test_minio_connection():
    """测试 MinIO 连接"""
    print("\n=== MinIO 连接测试 ===")
    try:
        from minio import Minio
        from minio.error import S3Error
        
        config = get_minio_config()
        endpoint = config.get("endpoint")
        access_key = config.get("access_key")
        secret_key = config.get("secret_key")
        bucket = config.get("bucket")
        
        # 处理 endpoint
        endpoint_no_proto = endpoint.replace("https://", "").replace("http://", "")
        use_https = endpoint.startswith("https://")
        
        print(f"连接到: {endpoint_no_proto} (HTTPS: {use_https})")
        
        # 创建客户端
        client = Minio(
            endpoint_no_proto,
            access_key=access_key,
            secret_key=secret_key,
            secure=use_https,
            cert_check=False
        )
        
        # 测试连接
        print("测试桶列表...")
        buckets = client.list_buckets()
        print(f"✓ 连接成功，找到 {len(buckets)} 个桶:")
        for bucket_info in buckets:
            print(f"  - {bucket_info.name} (创建时间: {bucket_info.creation_date})")
        
        # 检查目标桶
        print(f"\n检查目标桶: {bucket}")
        if client.bucket_exists(bucket):
            print(f"✓ 桶 '{bucket}' 存在")
        else:
            print(f"✗ 桶 '{bucket}' 不存在，尝试创建...")
            client.make_bucket(bucket)
            print(f"✓ 桶 '{bucket}' 创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {str(e)}")
        return False

def test_upload():
    """测试文件上传"""
    print("\n=== 文件上传测试 ===")
    try:
        # 创建测试数据
        test_data = b"Hello, MinIO! This is a test file."
        object_name = "test/test_file.txt"
        
        print(f"上传测试文件: {object_name}")
        url = upload_audio_to_minio(
            object_name=object_name,
            data=test_data,
            content_type="text/plain"
        )
        
        print(f"✓ 上传成功，URL: {url}")
        return True
        
    except Exception as e:
        print(f"✗ 上传失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("MinIO 诊断工具")
    print("=" * 50)
    
    # 测试配置
    if not test_minio_config():
        print("\n❌ 配置测试失败，请检查 config.toml 中的 [minio] 配置")
        return
    
    # 测试连接
    if not test_minio_connection():
        print("\n❌ 连接测试失败，请检查网络和认证信息")
        return
    
    # 测试上传
    if not test_upload():
        print("\n❌ 上传测试失败")
        return
    
    print("\n✅ 所有测试通过！MinIO 配置正常")

if __name__ == "__main__":
    main()
