// 对话项类型
export interface DialogueItem {
  role: string;
  text: string;
}

// 文本内容类型
export interface TextContent {
  title: string;
  labels: {
    articleInput: string;
    generateScript: string;
    jsonInput: string;
    submit: string;
    loadExample: string;
  };
  placeholders: {
    jsonInput: string;
  };
}

// 角色声音映射类型
export interface VoiceRoleItem {
  key: string;
  desc: string;
}

// API响应类型
export interface ApiResponse {
  status: string;
  audio: string;
}

export interface ApiErrorResponse {
  error: string;
  status: string;
}
/**
 * AI 视频基础信息
 */
/**
 * AIVideo 新结构（与后端 models.py 完全一致）
 */
export interface AIVideo {
  id: number;
  type: string;
  input: string;
  title: string;
  content: string;
  cap?: string;
  storyboard_list?: any[]; // 分镜描述列表
  image_list?: any[];      // 图片列表
  audio_list?: any[];      // 配音列表
  duration_list?: any[];   // 时间戳列表
  draft_url?: string;
  debug_url?: string;
  created_at: string;
}

/**
 * AI 视频详情响应
 */
export interface AIVideoDetailResponse {
  data: AIVideo;
}

/**
 * AI 视频列表分页响应
 */
export interface AIVideoListResponse {
  data: AIVideo[];
  total: number;
  page: number;
  limit: number;
}