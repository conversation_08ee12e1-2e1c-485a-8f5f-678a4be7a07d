/**
 * 通用下载函数，支持图片/音频等静态资源，采用 fetch blob 方式，失败弹窗提示。
 */
export async function handleDownload(url: string) {
  try {
    const res = await fetch(url, { mode: 'cors' });
    if (!res.ok) throw new Error(`下载失败，状态码：${res.status}`);
    const blob = await res.blob();
    const blobUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = blobUrl;
    let finalFilename: string;
    try {
      // 更健壮地从 url 路径提取文件名，去除 query/hash
      const urlObj = new URL(url, window.location.href);
      const pathname = urlObj.pathname;
      const name = pathname.substring(pathname.lastIndexOf('/') + 1);
      finalFilename = name || 'download';
    } catch (e) {
      // fallback
      finalFilename = url.split('/').pop() || 'download';
    }
    a.download = finalFilename;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(blobUrl);
    }, 100);
  } catch (e: any) {
    window.alert(e?.message || '下载失败');
  }
}

/**
 * 类型映射工具函数：根据 videoStyles 映射 type 到 name，未找到则回退原 type
 */
export function getTypeName(type: string, videoStyles: {type: string, name: string}[]): string {
  const found = videoStyles.find(style => style.type === type);
  return found ? found.name : type;
}