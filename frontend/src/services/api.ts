import axios from 'axios';
import { DialogueItem, ApiResponse, VoiceRoleItem, AIVideo, AIVideoDetailResponse, AIVideoListResponse } from '../types';

// API基础URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5008/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 生成音频
 * @param jsonDialogue 对话JSON字符串
 * @param language 语言('en'或'zh')
 * @param voiceMap 角色-voice对象数组（可选），每项含 key/desc
 * @returns 包含音频数据的响应
 */
export const generateAudio = async (
  jsonDialogue: string,
  language: string,
  voiceMap?: VoiceRoleItem[]
): Promise<ApiResponse> => {
  try {
    const payload: any = {
      json_dialogue: jsonDialogue,
      language: language,
    };
    if (voiceMap) {
      payload.voice_map = JSON.stringify(voiceMap);
    }
    const response = await apiClient.post<ApiResponse>('/podcast/generate/audio', payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Failed to generate audio');
    }
    throw new Error('Network error occurred');
  }
};

/**
 * 生成播客脚本
 * @param inputText 输入的文章文本
 * @param language 语言('en'或'zh')
 * @returns 包含脚本数据的响应
 */
export const generateScript = async (inputText: string, language: string): Promise<{script: DialogueItem[]}> => {
  try {
    const response = await apiClient.post('/podcast/generate/script', {
      input_text: inputText,
      language: language
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'Failed to generate script');
    }
    throw new Error('Network error occurred');
  }
};

/**
 * 获取所有可用角色
 * @param lang 可选，指定语言（如 'zh' 或 'en'），只返回该语言数据
 * @returns 角色对象，结构为 { zh: [{ role, voices }], en?: [{ role, voices }] }
 */
export const getVoiceRoles = async (
  lang?: string
): Promise<{
  zh: { role: string; voices: VoiceRoleItem[] }[];
  en?: { role: string; voices: VoiceRoleItem[] }[];
}> => {
  try {
    const url = `${API_BASE_URL}/podcast/voice_roles${lang ? `?lang=${encodeURIComponent(lang)}` : ''}`;
    const response = await axios.get(url);
    const data = response.data || {};

    // 处理API实际返回的格式: { zh: [{ role: "0", voices: [{ key, desc }] }], en?: [...] }
    if (data.zh && Array.isArray(data.zh)) {
      const processedZh = data.zh.map((roleData: any) => ({
        role: roleData.role || '0',
        voices: (roleData.voices || []).map((voice: any) => ({
          key: voice.key || '',
          desc: voice.desc || ''
        }))
      }));

      const result: {
        zh: { role: string; voices: VoiceRoleItem[] }[];
        en?: { role: string; voices: VoiceRoleItem[] }[];
      } = { zh: processedZh };

      // 如果有英文数据也处理
      if (data.en && Array.isArray(data.en)) {
        result.en = data.en.map((roleData: any) => ({
          role: roleData.role || '0',
          voices: (roleData.voices || []).map((voice: any) => ({
            key: voice.key || '',
            desc: voice.desc || ''
          }))
        }));
      }

      return result;
    }

    // 如果没有预期的数据结构，返回空结构
    return { zh: [] };
  } catch (error) {
    throw new Error('无法获取角色列表');
  }
};

// 获取脚本历史记录
export const getScriptHistory = async (): Promise<any[]> => {
  try {
    const response = await apiClient.get('/podcast/history/scripts');
    return response.data;
  } catch (error) {
    throw new Error('无法获取脚本历史记录');
  }
};

// 获取音频历史记录
export const getAudioHistory = async (): Promise<any[]> => {
  try {
    const response = await apiClient.get('/podcast/history/audios');
    return response.data;
  } catch (error) {
    throw new Error('无法获取音频历史记录');
  }
};

/**
 * 音频后期合成
 * @param id 音频ID
 * @returns 接口响应
 */
export const editAudio = async (id: number): Promise<any> => {
  try {
    const payload = { id };
    const response = await apiClient.post(
      '/podcast/edit/audio',
      payload,
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || '音频后期合成失败');
    }
    throw new Error('网络错误');
  }
};

// 获取TTS语音生成历史（分页）
export const getTTSHistory = async (page: number = 1, limit: number = 10): Promise<{
  data: {
    id: number;
    text: string;
    voice: string;
    created_at: string;
    audio_url: string;
  }[];
  total: number;
  page: number;
  limit: number;
}> => {
  try {
    const response = await apiClient.get('/tts/history', {
      params: { page, limit }
    });
    return response.data;
  } catch (error) {
    throw new Error('无法获取TTS语音生成历史');
  }
};

/**
 * 获取TTS可用voice列表，返回 VoiceRoleItem[]，每项含 key/desc。
 */
export const getTTSVoices = async (): Promise<VoiceRoleItem[]> => {
  try {
    const response = await apiClient.get('/tts/voices');
    return response.data.voices || [];
  } catch (error) {
    throw new Error('无法获取TTS语音列表');
  }
};

// 生成TTS音频（voice 参数为 voice key）
export const generateTTS = async (text: string, voiceKey: string): Promise<{ audio_url: string }> => {
  try {
    const response = await apiClient.post('/tts/generate', { text, voice: voiceKey });
    return response.data;
  } catch (error) {
    throw new Error('TTS音频生成失败');
  }
};

/**
 * 获取AI视频列表（分页）
 * @param page 页码
 * @param limit 每页数量
 */
export const getAIVideoList = async (
  page: number = 1,
  limit: number = 10
): Promise<AIVideoListResponse> => {
  try {
    const response = await apiClient.get('/aivideo/list', {
      params: { page, limit }
    });
    // 统一新结构
    return response.data as AIVideoListResponse;
  } catch (error) {
    throw new Error('无法获取AI视频列表');
  }
};

/**
 * 获取AI视频详情
 * @param id 视频ID
 */
export const getAIVideoDetail = async (
  id: number | string
): Promise<AIVideoDetailResponse> => {
  try {
    const response = await apiClient.get('/aivideo/detail', {
      params: { id }
    });
    // 类型安全处理 storyboard_list
    const data = response.data;
    if (data.data?.storyboard_list && typeof data.data?.storyboard_list === 'string') {
      data.data.storyboard_list = JSON.parse(data.data.storyboard_list);
    }
    if (data.data?.duration_list && typeof data.data?.duration_list === 'string') {
      data.data.duration_list = JSON.parse(data.data.duration_list);
    }
    
    return data as AIVideoDetailResponse;
  } catch (error) {
    throw new Error('无法获取AI视频详情');
  }
};

/**
 * 替换AI视频素材
 * @param asset_id 素材ID
 * @param type 素材类型（'audio' 或 'image'）
 * @param content 新内容（图片为新URL及prompt，音频为新URL及text）
 * @param idx 素材在 JSON 列表中的索引（对应后端 asset_index，表示要替换的素材在 image_list/audio_list 中的位置，从 0 开始）
 * @returns Promise<{ code: number, msg: string, data?: { url: string } }>
 *
 * 说明：
 * - idx 参数用于指定要替换的素材在 JSON 列表中的索引位置，需与后端 asset_index 保持一致。
 * - 例如：替换第 n 个音频或图片时，idx 传 n（从 0 开始），后端将根据 asset_index 定位并替换对应素材。
 * - 该接口仅支持音频（audio）和图片（image）类型的素材替换。
 */
export const replaceAsset = async (
    asset_id: number | string,
    type: string,
    content: string,
    idx: number // 素材在 JSON 列表中的索引（对应后端 asset_index）
): Promise<{ code: number; msg: string; data?: { url: string } }> => {
    try {
        const payload = { asset_id, type, content, asset_index: idx }; // payload 增加 asset_index 字段
        const response = await apiClient.post('/aivideo/replace_asset', payload, {
            headers: { 'Content-Type': 'application/json' }
        });
        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error) && error.response) {
            throw new Error(error.response.data.error || '素材替换失败');
        }
        throw new Error('网络错误');
    }
};

/**
 * 两步法第一步：生成AI视频脚本
 * @param input 输入主题
 * @returns Promise<{ code: number, msg: string }>
 */
export const generateAIVideoPart1 = async (
  input: string,
  type?: string
): Promise<{ data: AIVideo; status: number }> => {
  try {
    const payload: { input: string; type?: string } = { input };
    if (type) {
      payload.type = type;
    }
    const response = await apiClient.post('/aivideo/generate_step1', payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    // 统一新结构
    if (response.status === 200) {
      return { data: response.data.data as AIVideo, status: response.status };
    } else {
      throw new Error(`AI视频脚本生成失败: status=${response.status}`);
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'AI视频脚本生成失败');
    }
    throw new Error('网络错误');
  }
};

/**
 * 两步法第二步：生成AI视频（根据脚本）
 * @param id 视频ID
 * @returns Promise<{ code: number, msg: string }>
 */
export const generateAIVideoPart2 = async (
  id: number | string,
  content?: string,
  step: number = 2
): Promise<{ data: AIVideo; status: number }> => {
  try {
    const payload: { id: number | string; content?: string; step: number } = { id, step };
    if (content !== undefined) {
      payload.content = content;
    }
    const response = await apiClient.post('/aivideo/generate_step2', payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    // 统一新结构
    if (response.status === 200) {
      return { data: response.data.data as AIVideo, status: response.status };
    } else {
      throw new Error(`AI视频第二步生成失败: status=${response.status}`);
    }
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || 'AI视频第二步生成失败');
    }
    throw new Error('网络错误');
  }
};

export const getVideoStyles = async (): Promise<{ type: string; name: string }[]> => {
  try {
    const response = await apiClient.get('/aivideo/video_styles');
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      throw new Error(error.response.data.error || '获取视频风格列表失败');
    }
    throw new Error('网络错误');
  }
}
/**
* 更新AI视频详情
* @param id 视频ID
* @param title 标题
* @param content 内容
* @returns Promise<{ status: number, data?: any, message?: string }>
*/
export async function updateAIVideoDetail(
  id: number | string,
  title: string,
  content: string
): Promise<any> {
  try {
    const payload = { id, title, content };
    const response = await apiClient.post('/aivideo/update', payload, {
      headers: { 'Content-Type': 'application/json' }
    });
    // 返回结构与后端一致
    return response.data;
  } catch (error: any) {
    if (axios.isAxiosError(error) && error.response) {
      // 优先返回后端 message 字段
      throw new Error(error.response.data?.message || error.response.data?.error || 'AI视频详情更新失败');
    }
    throw new Error('网络错误');
  }
}