import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, OverlayTrigger, Popover, Modal, Tabs, Tab } from 'react-bootstrap';
import { getScriptHistory, getAudioHistory, editAudio, getTTSHistory } from '../services/api';

function TextPreview({ text, maxLen = 50 }: { text: string, maxLen?: number }) {
  const show = text && text.length > maxLen ? text.slice(0, maxLen) + '...' : text;
  const popover = (
    <Popover className="aipodcast-popover-wide">
      <Popover.Body
        style={{
          maxWidth: 900,
          maxHeight: 320,
          overflow: 'auto',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all',
        }}
      >
        {text}
      </Popover.Body>
    </Popover>
  );
  return (
    <span style={{ display: 'inline-flex', alignItems: 'center' }}>
      <span>{show}</span>
      {text && text.length > maxLen && (
        <OverlayTrigger trigger="click" placement="auto" overlay={popover} rootClose>
          <Button
            variant="link"
            size="sm"
            style={{ padding: '0 0.2rem', marginLeft: 2, color: '#007bff', verticalAlign: 'middle' }}
            tabIndex={0}
            aria-label="查看详情"
          >
            {/* 内联 Eye 图标 */}
            <svg width="1.2em" height="1.2em" viewBox="0 0 16 16" fill="currentColor" style={{ pointerEvents: 'none' }}>
              <path d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zm-8 4.5c-3.314 0-6-3.072-6-4.5s2.686-4.5 6-4.5 6 3.072 6 4.5-2.686 4.5-6 4.5z"/>
              <path d="M8 5.5A2.5 2.5 0 1 0 8 10a2.5 2.5 0 0 0 0-4.5zm0 1A1.5 1.5 0 1 1 8 9a1.5 1.5 0 0 1 0-3z"/>
            </svg>
          </Button>
        </OverlayTrigger>
      )}
    </span>
  );
}

function VoiceModelCell({ value, maxLen = 50 }: { value: string, maxLen?: number }) {
  if (!value) return null;
  return (
    <div style={{ minWidth: 0 }}>
      {value.split(',').map((v, i) => {
        const trimmed = v.trim();
        const show = trimmed.length > maxLen ? trimmed.slice(0, maxLen) + '...' : trimmed;
        return (
          <div
            key={i}
            style={{
              fontSize: '0.97em',
              lineHeight: 1.3,
              color: '#8498dfff',
              wordBreak: 'break-all',
              marginBottom: i === value.split(',').length - 1 ? 0 : 2,
              maxWidth: 200,
            }}
            title={trimmed}
          >
            {show}
          </div>
        );
      })}
    </div>
  );
}

function formatTime(iso: string) {
  if (!iso) return '';
  const d = new Date(iso);
  if (isNaN(d.getTime())) return iso;
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }).replace(/\//g, '-');
}

function History() {
  // tab 状态
  const [activeTab, setActiveTab] = useState<'tts' | 'audio' | 'script'>('tts');

  // TTS 语音生成历史
  const [ttsHistory, setTTSHistory] = useState<any[]>([]);
  const [ttsPage, setTTSPage] = useState(1);
  const [ttsTotal, setTTSTotal] = useState(0);
  const [ttsLoading, setTTSLoading] = useState(false);
  const [ttsError, setTTSError] = useState<string | null>(null);
  const TTS_PAGE_SIZE = 10;

  // 其他历史
  const [scriptHistory, setScriptHistory] = useState<any[]>([]);
  const [audioHistory, setAudioHistory] = useState<any[]>([]);
  const [historyLoading, setHistoryLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 合成按钮 loading 状态，key 为音频 id
  const [editLoading, setEditLoading] = useState<{ [id: number]: boolean }>({});

  // 音频播放器弹窗状态
  const [showPlayer, setShowPlayer] = useState(false);
  const [currentAudioUrl, setCurrentAudioUrl] = useState<string | null>(null);

  // TTS 历史加载
  const fetchTTSHistory = async (page = 1, append = false) => {
    setTTSLoading(true);
    setTTSError(null);
    try {
      const res = await getTTSHistory(page, TTS_PAGE_SIZE);
      setTTSPage(page);
      setTTSTotal(res.total || 0);
      if (append) {
        setTTSHistory(prev => [...prev, ...(res.data || [])]);
      } else {
        setTTSHistory(res.data || []);
      }
    } catch (e: any) {
      setTTSError(e?.message || '获取TTS语音生成历史失败');
    } finally {
      setTTSLoading(false);
    }
  };

  // TTS tab 首次加载
  useEffect(() => {
    if (activeTab === 'tts' && ttsHistory.length === 0 && !ttsLoading) {
      fetchTTSHistory(1, false);
    }
    // eslint-disable-next-line
  }, [activeTab]);

  // 其他历史加载
  useEffect(() => {
    setHistoryLoading(true);
    Promise.all([getScriptHistory(), getAudioHistory()])
      .then(([scripts, audios]: [any, any]) => {
        setScriptHistory(scripts);
        // 兼容 audios 为 { data: [...] } 或直接为数组，确保 audioHistory 始终为数组
        setAudioHistory(Array.isArray(audios) ? audios : (Array.isArray(audios?.data) ? audios.data : []));
      })
      .catch(() => setError('获取历史记录失败'))
      .finally(() => setHistoryLoading(false));
  }, []);

  // 合成音频接口
  async function handleEditAudio(item: any) {
    setEditLoading((prev) => ({ ...prev, [item.id]: true }));
    try {
      const data = await editAudio(item.id);
      setAudioHistory((prev) =>
        prev.map((row) =>
          row.id === item.id
            ? { ...row, edit_audio_url: data.edit_audio_url }
            : row
        )
      );
    } catch (e) {
      alert('音频后期合成失败');
    } finally {
      setEditLoading((prev) => ({ ...prev, [item.id]: false }));
    }
  }

  // 播放按钮点击：弹出播放器并自动播放
  const handlePlayClick = (item: any, urlField: string = 'audio_url') => {
    if (item && item[urlField]) {
      setCurrentAudioUrl(item[urlField]);
      setShowPlayer(true);
    }
  };

  // 关闭播放器
  const handleClosePlayer = () => {
    setShowPlayer(false);
    setCurrentAudioUrl(null);
  };

  // 下载按钮点击
  const handleDownloadClick = (item: any, urlField: string = 'audio_url', defaultName = 'audio.mp3') => {
    if (!item || !item[urlField]) return;
    let filename = defaultName;
    try {
      const urlObj = new URL(item[urlField], window.location.href);
      const pathname = urlObj.pathname;
      const name = pathname.substring(pathname.lastIndexOf('/') + 1);
      if (name && name.includes('.')) filename = decodeURIComponent(name);
    } catch (e) {}
    const a = document.createElement('a');
    a.href = item[urlField];
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // TTS 加载更多
  const handleLoadMoreTTS = () => {
    if (ttsHistory.length < ttsTotal && !ttsLoading) {
      fetchTTSHistory(ttsPage + 1, true);
    }
  };

  return (
    <div className="aipodcast-card">
      {error && (
        <Alert variant="danger" className="mb-3">
          {error}
        </Alert>
      )}
      {/* 音频播放器弹窗 */}
      <Modal
        show={showPlayer}
        onHide={handleClosePlayer}
        centered
        backdrop="static"
        keyboard
      >
        <Modal.Header closeButton>
          <Modal.Title>音频播放</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {currentAudioUrl ? (
            <audio
              key={currentAudioUrl}
              src={currentAudioUrl}
              controls
              autoPlay
              style={{ width: '100%' }}
            />
          ) : (
            <div>未找到音频链接</div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleClosePlayer}>
            关闭
          </Button>
        </Modal.Footer>
      </Modal>
      <Tabs
        id="history-tabs"
        activeKey={activeTab}
        onSelect={k => setActiveTab((k as 'tts' | 'audio' | 'script') || 'tts')}
        className="mb-3"
      >
        <Tab eventKey="tts" title="语音合成记录">
          {ttsError && (
            <Alert variant="danger" className="mb-3">
              {ttsError}
            </Alert>
          )}
          <div className="table-responsive mb-2">
            <table className="table table-bordered table-sm aipodcast-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>文本</th>
                  <th>voice</th>
                  <th>音频</th>
                  <th>生成时间</th>
                </tr>
              </thead>
              <tbody>
                {ttsHistory.length === 0 && !ttsLoading ? (
                  <tr>
                    <td colSpan={5} className="text-center text-muted">暂无数据</td>
                  </tr>
                ) : (
                  ttsHistory.map((item: any) => (
                    <tr key={item.id}>
                      <td>{item.id}</td>
                      <td style={{ maxWidth: 200, wordBreak: 'break-all' }}>
                        <TextPreview text={item.text} />
                      </td>
                      <td style={{ maxWidth: 120, wordBreak: 'break-all' }}>
                        {item.voice}
                      </td>
                      <td>
                        {item.audio_url ? (
                          <div style={{ display: 'flex', gap: 12 }}>
                            <button
                              type="button"
                              onClick={() => handlePlayClick(item, 'audio_url')}
                              style={{
                                background: 'none',
                                border: 'none',
                                padding: 0,
                                margin: 0,
                                cursor: 'pointer',
                                color: '#007bff',
                                display: 'flex',
                                alignItems: 'center',
                                fontSize: 18,
                              }}
                              title="播放"
                              aria-label="播放"
                            >
                              <svg width="1.4em" height="1.4em" viewBox="0 0 20 20" fill="currentColor">
                                <polygon points="5,3 17,10 5,17" />
                              </svg>
                            </button>
                            <button
                              type="button"
                              onClick={() => handleDownloadClick(item, 'audio_url', 'tts_audio.mp3')}
                              style={{
                                background: 'none',
                                border: 'none',
                                padding: 0,
                                margin: 0,
                                cursor: 'pointer',
                                color: '#28a745',
                                display: 'flex',
                                alignItems: 'center',
                                fontSize: 18,
                              }}
                              title="下载"
                              aria-label="下载"
                            >
                              <svg width="1.3em" height="1.3em" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 3v10M10 13l-4-4m4 4l4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                                <rect x="4" y="15" width="12" height="2" rx="1" fill="currentColor"/>
                              </svg>
                            </button>
                          </div>
                        ) : (
                          <span className="text-muted">无音频</span>
                        )}
                      </td>
                      <td className="text-secondary">{formatTime(item.created_at)}</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
            {ttsLoading && (
              <div className="text-center my-2">
                <Spinner animation="border" size="sm" />
              </div>
            )}
            {ttsHistory.length < ttsTotal && !ttsLoading && (
              <div className="text-center my-2">
                <Button variant="outline-primary" size="sm" onClick={handleLoadMoreTTS}>
                  加载更多
                </Button>
              </div>
            )}
          </div>
        </Tab>
        <Tab eventKey="audio" title="音频记录">
          {historyLoading ? (
            <Spinner animation="border" />
          ) : (
            <div>
              <div className="aipodcast-section-title" style={{ fontSize: '1.15rem', marginBottom: '0.7rem', marginTop: '0.5rem' }}>音频记录</div>
              <div className="table-responsive mb-4">
                <table className="table table-bordered table-sm aipodcast-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>脚本文本</th>
                      <th>声音模型</th>
                      <th>播客音频</th>
                      <th>后期合成</th>
                      <th>创建时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {audioHistory.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="text-center text-muted">暂无数据</td>
                      </tr>
                    ) : (
                      audioHistory.map((item: any) => (
                        <tr key={item.id}>
                          <td>{item.id}</td>
                          <td style={{ maxWidth: 200, wordBreak: 'break-all' }}>
                            <TextPreview text={item.script_text} />
                          </td>
                          <td style={{ maxWidth: 200, wordBreak: 'break-all' }} className="text-secondary">
                            <VoiceModelCell value={item.voice_model_name} />
                          </td>
                          <td>
                            <div style={{ display: 'flex', gap: 20 }}>
                              <button
                                type="button"
                                onClick={() => handlePlayClick(item)}
                                style={{
                                  background: 'none',
                                  border: 'none',
                                  padding: 0,
                                  margin: 0,
                                  cursor: 'pointer',
                                  color: '#007bff',
                                  display: 'flex',
                                  alignItems: 'center',
                                  fontSize: 18,
                                }}
                                title="播放"
                                aria-label="播放"
                              >
                                <svg width="1.4em" height="1.4em" viewBox="0 0 20 20" fill="currentColor">
                                  <polygon points="5,3 17,10 5,17" />
                                </svg>
                              </button>
                              <button
                                type="button"
                                onClick={() => handleDownloadClick(item)}
                                style={{
                                  background: 'none',
                                  border: 'none',
                                  padding: 0,
                                  margin: 0,
                                  cursor: 'pointer',
                                  color: '#28a745',
                                  display: 'flex',
                                  alignItems: 'center',
                                  fontSize: 18,
                                }}
                                title="下载"
                                aria-label="下载"
                              >
                                <svg width="1.3em" height="1.3em" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M10 3v10M10 13l-4-4m4 4l4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                                  <rect x="4" y="15" width="12" height="2" rx="1" fill="currentColor"/>
                                </svg>
                              </button>
                            </div>
                          </td>
                          <td>
                            {typeof item.edit_audio_url === 'string' && item.edit_audio_url.trim() ? (
                              <div style={{ display: 'flex', gap: 20 }}>
                                <button
                                  type="button"
                                  onClick={() => {
                                    setCurrentAudioUrl(item.edit_audio_url);
                                    setShowPlayer(true);
                                  }}
                                  style={{
                                    background: 'none',
                                    border: 'none',
                                    padding: 0,
                                    margin: 0,
                                    cursor: 'pointer',
                                    color: '#007bff',
                                    display: 'flex',
                                    alignItems: 'center',
                                    fontSize: 18,
                                  }}
                                  title="播放"
                                  aria-label="播放"
                                >
                                  <svg width="1.4em" height="1.4em" viewBox="0 0 20 20" fill="currentColor">
                                    <polygon points="5,3 17,10 5,17" />
                                  </svg>
                                </button>
                                <button
                                  type="button"
                                  onClick={() => {
                                    let filename = 'audio_edit.mp3';
                                    try {
                                      const urlObj = new URL(item.edit_audio_url, window.location.href);
                                      const pathname = urlObj.pathname;
                                      const name = pathname.substring(pathname.lastIndexOf('/') + 1);
                                      if (name && name.includes('.')) filename = decodeURIComponent(name);
                                    } catch (e) {}
                                    const a = document.createElement('a');
                                    a.href = item.edit_audio_url;
                                    a.download = filename;
                                    document.body.appendChild(a);
                                    a.click();
                                    document.body.removeChild(a);
                                  }}
                                  style={{
                                    background: 'none',
                                    border: 'none',
                                    padding: 0,
                                    margin: 0,
                                    cursor: 'pointer',
                                    color: '#28a745',
                                    display: 'flex',
                                    alignItems: 'center',
                                    fontSize: 18,
                                  }}
                                  title="下载"
                                  aria-label="下载"
                                >
                                  <svg width="1.3em" height="1.3em" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10 3v10M10 13l-4-4m4 4l4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                                    <rect x="4" y="15" width="12" height="2" rx="1" fill="currentColor"/>
                                  </svg>
                                </button>
                              </div>
                            ) : (
                              <Button
                                variant="primary"
                                size="sm"
                                disabled={!!editLoading[item.id]}
                                onClick={() => handleEditAudio(item)}
                              >
                                {editLoading[item.id] ? (
                                  <>
                                    <Spinner
                                      as="span"
                                      animation="border"
                                      size="sm"
                                      role="status"
                                      aria-hidden="true"
                                      style={{ marginRight: 6 }}
                                    />
                                    合成中...
                                  </>
                                ) : (
                                  '合成'
                                )}
                              </Button>
                            )}
                          </td>
                          <td className="text-secondary">{formatTime(item.created_at)}</td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </Tab>
        <Tab eventKey="script" title="脚本记录">
          {historyLoading ? (
            <Spinner animation="border" />
          ) : (
            <div>
              <div className="aipodcast-section-title" style={{ fontSize: '1.15rem', marginBottom: '0.7rem', marginTop: '1.2rem' }}>脚本记录</div>
              <div className="table-responsive mb-4">
                <table className="table table-bordered table-sm aipodcast-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>原始文本</th>
                      <th>摘要</th>
                      <th>脚本文本</th>
                      <th>创建时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {scriptHistory.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="text-center text-muted">暂无数据</td>
                      </tr>
                    ) : (
                      scriptHistory.map((item: any) => (
                        <tr key={item.id}>
                          <td>{item.id}</td>
                          <td style={{ maxWidth: 200, wordBreak: 'break-all' }}>
                            <TextPreview text={item.original_text} />
                          </td>
                          <td style={{ maxWidth: 200, wordBreak: 'break-all' }}>
                            <TextPreview text={item.summary_text} />
                          </td>
                          <td style={{ maxWidth: 200, wordBreak: 'break-all' }}>
                            <TextPreview text={item.script_text} />
                          </td>
                          <td className="text-secondary">{formatTime(item.created_at)}</td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </Tab>
      </Tabs>
    </div>
  );
}

export default History;