'use client';
import React, { useEffect, useState } from 'react';
import { <PERSON>, Pa<PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>, Mo<PERSON>, Button, Form } from 'react-bootstrap';
import { getAIVideoList, generateAIVideoPart1, getVideoStyles } from '../services/api';
import { AIVideo } from '../types';
import { useRouter } from 'next/navigation';

import { getTypeName } from '../utils';

interface ListState {
  data: AIVideo[];
  total: number;
  page: number;
  limit: number;
  loading: boolean;
  error: string | null;
}

/**
 * 格式化日期为“YYYY-MM-DD”或“YYYY年MM月DD日”格式，兼容字符串和Date对象
 */
function formatDate(date: string | Date | undefined): string {
  if (!date) return '';
  let d: Date;
  if (typeof date === 'string') {
    // 兼容ISO字符串
    d = new Date(date);
    if (isNaN(d.getTime())) return date; // 解析失败，原样返回
  } else if (date instanceof Date) {
    d = date;
  } else {
    return '';
  }
  // 输出格式：YYYY-MM-DD HH:mm
  const y = d.getFullYear();
  const m = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  const hh = d.getHours().toString().padStart(2, '0');
  const mm = d.getMinutes().toString().padStart(2, '0');
  return `${y}-${m}-${day} ${hh}:${mm}`;
}

const AIVideoList: React.FC<{ onSelectDetail?: (id: number) => void }> = ({ onSelectDetail }) => {
  const router = useRouter();
  const [state, setState] = useState<ListState>({
    data: [],
    total: 0,
    page: 1,
    limit: 10,
    loading: false,
    error: null,
  });

  // 新增：生成相关状态
  const [showModal, setShowModal] = useState(false);
  const [form, setForm] = useState({ input: '', style: '' });
  const [genLoading, setGenLoading] = useState(false);
  const [genError, setGenError] = useState<string | null>(null);
  const [genSuccess, setGenSuccess] = useState<string | null>(null);

  const [videoStyles, setVideoStyles] = useState<{ type: string; name: string }[]>([]);
  const [videoStylesLoading, setVideoStylesLoading] = useState(false);
  const [videoStylesError, setVideoStylesError] = useState<string | null>(null);

  // 类型映射辅助函数已提取至 utils
  
  const fetchList = async (page: number, limit: number) => {
      setState(s => ({ ...s, loading: true, error: null }));
      try {
        const res = await getAIVideoList(page, limit);
        setState(s => ({
          ...s,
          data: res.data,
          total: res.total,
          page: res.page,
          limit: res.limit,
          loading: false,
          error: null,
        }));
      } catch (e: any) {
        setState(s => ({ ...s, loading: false, error: e.message || '获取AI视频列表失败' }));
      }
    };

  useEffect(() => {
    fetchList(state.page, state.limit);
    // eslint-disable-next-line
  }, []);

  // 异步获取视频风格列表
  useEffect(() => {
    setVideoStylesLoading(true);
    setVideoStylesError(null);
    getVideoStyles()
      .then(res => {
        setVideoStyles(res || []);
        setVideoStylesLoading(false);
      })
      .catch(e => {
        setVideoStylesError(e.message || '获取视频风格失败');
        setVideoStylesLoading(false);
      });
  }, []);


  const handlePageChange = (page: number) => {
    fetchList(page, state.limit);
  };

  const handleRowClick = (id: number) => {
    if (onSelectDetail) {
      onSelectDetail(id);
    } else {
      router.push(`/aivideo/${id}`);
    }
  };

  // 生成入口相关
  const handleOpenModal = () => {
    setForm({ input: '', style: '' });
    setGenError(null);
    setGenSuccess(null);
    setShowModal(true);
  };
  const handleCloseModal = () => {
    setShowModal(false);
    setGenError(null);
    setGenSuccess(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };
  const handleStyleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setForm({ ...form, style: e.target.value });
  };


  const handleGen = async () => {
    if (!form.input.trim()) {
      setGenError('主题不能为空');
      return;
    }
    setGenLoading(true);
    setGenError(null);
    setGenSuccess(null);
    try {
      const res = await generateAIVideoPart1(form.input, form.style);
      if (res.status === 200) {
        setGenSuccess('脚本生成成功！请在详情页完成后续三步。');
        fetchList(1, state.limit);
        setTimeout(() => {
          setShowModal(false);
        }, 1000);
      } else {
        setGenError('生成失败');
      }
    } catch (e: any) {
      setGenError(e.message || '生成失败');
    } finally {
      setGenLoading(false);
    }
  };

  // 计算分页
  const totalPages = Math.ceil(state.total / state.limit);

  return (
    <div className="aipodcast-card" style={{ width: '100%', padding: 10 }}>
      {/* 顶部生成入口 */}
      <div className="d-flex align-items-center mb-3 flex-wrap" style={{ gap: 16 }}>
        <Button
          variant="success"
          size="sm"
          style={{ borderRadius: 16, fontWeight: 600, minWidth: 120 }}
          onClick={handleOpenModal}
          disabled={genLoading}
          aria-label="生成视频文案（第一步）"
        >
          {genLoading ? (
            <>
              <Spinner animation="border" size="sm" className="me-2" />
              生成中...
            </>
          ) : (
            '生成视频文案（第一步）'
          )}
        </Button>
      </div>
      <div className="mb-2" style={{ fontSize: 14, color: '#888' }}>
        当前流程：<b>四步法</b>。仅在本页完成第一步（生成视频文案），后续三步请前往详情页依次完成。
      </div>

      {/* 生成弹窗 */}
      <Modal show={showModal} onHide={handleCloseModal} centered>
        <Modal.Header closeButton>
          <Modal.Title>
            第一步：生成视频文案
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-2" style={{ fontSize: 13, color: '#888' }}>
            请输入主题，点击生成后将生成视频文案，后续三步请前往详情页依次完成。
          </div>
          <Form autoComplete="off">
            <Form.Group className="mb-3" controlId="formInput">
              <Form.Label>主题</Form.Label>
              <Form.Control
                type="text"
                name="input"
                value={form.input}
                onChange={handleInputChange}
                placeholder="请输入视频主题"
                disabled={genLoading}
                autoFocus
                maxLength={40}
                aria-required="true"
                aria-label="视频主题"
                style={{ fontSize: 15 }}
              />
            </Form.Group>
            <Form.Group className="mb-3" controlId="formStyle">
              <Form.Label>视频风格</Form.Label>
              {videoStylesLoading ? (
                <div style={{ fontSize: 14, color: '#888' }}>
                  <Spinner animation="border" size="sm" className="me-2" />
                  正在加载风格...
                </div>
              ) : videoStylesError ? (
                <Alert variant="danger" className="py-2" style={{ fontSize: 14 }}>
                  {videoStylesError}
                </Alert>
              ) : (
                <Form.Select
                  name="style"
                  value={form.style}
                  onChange={handleStyleChange}
                  disabled={genLoading || videoStyles.length === 0}
                  aria-label="选择视频风格"
                  style={{ fontSize: 15 }}
                >
                  <option value="">请选择视频风格</option>
                  {videoStyles.map(style => (
                    <option key={style.type} value={style.type}>
                      {style.name}
                    </option>
                  ))}
                </Form.Select>
              )}
            </Form.Group>
            {genError && (
              <Alert variant="danger" className="py-2" style={{ fontSize: 14 }}>
                {genError}
              </Alert>
            )}
            {genSuccess && (
              <Alert variant="success" className="py-2" style={{ fontSize: 14 }}>
                {genSuccess}
              </Alert>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseModal} disabled={genLoading}>
            取消
          </Button>
          <Button
            variant="primary"
            onClick={handleGen}
            disabled={genLoading}
            style={{ minWidth: 90 }}
            aria-label="生成视频文案"
          >
            {genLoading ? (
              <>
                <Spinner animation="border" size="sm" className="me-2" />
                生成中...
              </>
            ) : (
              '生成视频文案'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      <div className="aipodcast-section-title mb-3">AI视频列表</div>
      {state.error && <Alert variant="danger" className="mb-3">{state.error}</Alert>}
      {state.loading ? (
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: 200 }}>
          <Spinner animation="border" />
        </div>
      ) : (
        <>
          <Table className="aipodcast-table mb-3" size="sm" responsive>
            <thead>
              <tr>
                <th style={{ width: 60 }}>ID</th>
                <th style={{ minWidth: 120 }}>类型</th>
                <th style={{ minWidth: 120 }}>主题</th>
                <th style={{ minWidth: 120 }}>标题</th>
                <th style={{ minWidth: 140 }}>创建时间</th>
              </tr>
            </thead>
            <tbody>
              {state.data.length === 0 ? (
                <tr>
                  <td colSpan={5} className="text-center text-muted">暂无数据</td>
                </tr>
              ) : (
                state.data.map(item => (
                  <tr
                    key={item.id}
                    style={{ cursor: 'pointer', transition: 'background 0.15s' }}
                    onClick={() => handleRowClick(item.id)}
                    className="table-row-hover"
                  >
                    <td>{item.id}</td>
                    <td>{getTypeName(item.type, videoStyles)}</td>
                    <td style={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{item.input}</td>
                    <td>{item.title}</td>
                    <td>{formatDate(item.created_at)}</td>
                  </tr>
                ))
              )}
            </tbody>
          </Table>
          {totalPages > 1 && (
            <Pagination className="justify-content-center mt-4" style={{ gap: 6 }}>
              <Pagination.First
                onClick={() => handlePageChange(1)}
                disabled={state.page === 1}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
              <Pagination.Prev
                onClick={() => handlePageChange(state.page - 1)}
                disabled={state.page === 1}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
              {Array.from({ length: totalPages }, (_, i) => (
                <Pagination.Item
                  key={i + 1}
                  active={state.page === i + 1}
                  onClick={() => handlePageChange(i + 1)}
                  className={state.page === i + 1 ? "aipodcast-btn-primary" : ""}
                  style={{ borderRadius: '0.7rem', minWidth: 36, fontWeight: 600 }}
                >
                  {i + 1}
                </Pagination.Item>
              ))}
              <Pagination.Next
                onClick={() => handlePageChange(state.page + 1)}
                disabled={state.page === totalPages}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
              <Pagination.Last
                onClick={() => handlePageChange(totalPages)}
                disabled={state.page === totalPages}
                className="aipodcast-btn-primary"
                style={{ borderRadius: '0.7rem', minWidth: 36 }}
              />
            </Pagination>
          )}
        </>
      )}
    </div>
  );
};

export default AIVideoList;