import React, { useEffect, useRef } from 'react';

type ToastType = 'success' | 'error';

interface SimpleToastProps {
  show: boolean;
  type?: ToastType;
  message: string;
  onClose: () => void;
  duration?: number; // 毫秒，默认 2500
}

const TYPE_STYLES: Record<ToastType, React.CSSProperties> = {
  success: {
    background: 'linear-gradient(90deg, #4caf50 60%, #81c784 100%)',
    color: '#fff',
    border: '1px solid #388e3c',
  },
  error: {
    background: 'linear-gradient(90deg, #f44336 60%, #e57373 100%)',
    color: '#fff',
    border: '1px solid #b71c1c',
  },
};

const toastContainerStyle: React.CSSProperties = {
  position: 'fixed',
  top: '32px',
  left: '50%',
  transform: 'translateX(-50%)',
  zIndex: 9999,
  minWidth: '240px',
  maxWidth: '80vw',
  boxShadow: '0 4px 16px rgba(0,0,0,0.18)',
  borderRadius: '8px',
  padding: '14px 24px 14px 18px',
  fontSize: '16px',
  display: 'flex',
  alignItems: 'center',
  gap: '12px',
  transition: 'opacity 0.3s, top 0.3s',
  opacity: 1,
  pointerEvents: 'auto',
};

const closeBtnStyle: React.CSSProperties = {
  marginLeft: 'auto',
  background: 'transparent',
  border: 'none',
  color: '#fff',
  fontSize: '18px',
  cursor: 'pointer',
  opacity: 0.7,
  transition: 'opacity 0.2s',
};

export const SimpleToast: React.FC<SimpleToastProps> = ({
  show,
  type = 'success',
  message,
  onClose,
  duration = 2500,
}) => {
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (show) {
      if (timerRef.current) clearTimeout(timerRef.current);
      timerRef.current = setTimeout(() => {
        onClose();
      }, duration);
    }
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [show, duration, onClose]);

  if (!show) return null;

  return (
    <div
      style={{
        ...toastContainerStyle,
        ...TYPE_STYLES[type],
      }}
      role="alert"
      aria-live="assertive"
    >
      <span style={{ fontWeight: 600, flex: 'none' }}>
        {type === 'success' ? '✔️' : '❌'}
      </span>
      <span style={{ flex: 1 }}>{message}</span>
      <button
        style={closeBtnStyle}
        aria-label="关闭"
        onClick={() => {
          if (timerRef.current) clearTimeout(timerRef.current);
          onClose();
        }}
        onMouseEnter={e => (e.currentTarget.style.opacity = '1')}
        onMouseLeave={e => (e.currentTarget.style.opacity = '0.7')}
      >
        ×
      </button>
    </div>
  );
};

export default SimpleToast;