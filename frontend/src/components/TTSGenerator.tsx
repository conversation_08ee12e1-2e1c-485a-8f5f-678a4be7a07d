import React, { useEffect, useState, useRef } from 'react';
import { Row, Col, <PERSON>, But<PERSON>, Spinner, Alert } from 'react-bootstrap';
import { VoiceRoleItem } from '../types';
import { getTTSVoices, generateTTS } from '../services/api';

const textContent = {
  title: "TTS语音生成",
  labels: {
    textInput: "输入文本",
    voiceSelect: "选择语音",
    submit: "生成语音"
  },
  placeholders: {
    textInput: "请输入要合成的文本"
  }
};

const TTSGenerator: React.FC = () => {
  const [text, setText] = useState('');
  const [voices, setVoices] = useState<VoiceRoleItem[]>([]);
  const [selectedVoice, setSelectedVoice] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingVoices, setIsLoadingVoices] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    setIsLoadingVoices(true);
    getTTSVoices()
      .then((voiceList) => {
        setVoices(voiceList);
        if (voiceList.length > 0) setSelectedVoice(voiceList[0].key);
      })
      .catch(() => setError('获取语音列表失败'))
      .finally(() => setIsLoadingVoices(false));
  }, []);

  useEffect(() => {
    return () => {
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setAudioUrl(null);
    setIsLoading(true);

    try {
      if (!text) {
        throw new Error('文本内容不能为空');
      }
      if (!selectedVoice) {
        throw new Error('请选择语音');
      }
      const res = await generateTTS(text, selectedVoice);
      if (res.audio_url && typeof res.audio_url === 'string') {
        setAudioUrl(res.audio_url);
        setTimeout(() => {
          if (audioRef.current) {
            audioRef.current.load();
            audioRef.current.play();
          }
        }, 0);
      } else {
        throw new Error('未获取到有效的音频数据');
      }
    } catch (e: any) {
      setError(e.message || '生成失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="aipodcast-card">
      {/* <div className="aipodcast-section-title mb-4">{textContent.title}</div> */}
      {error && (
        <Alert variant="danger" className="mb-3 aipodcast-alert">
          {error}
        </Alert>
      )}
      <Form onSubmit={handleSubmit}>
        <Row className="g-4 flex-lg-nowrap">
          <Col xs={12} lg={7} className="d-flex flex-column">
            <div>
              <div className="aipodcast-form-label">{textContent.labels.textInput}</div>
              <Form.Group>
                <Form.Control
                  as="textarea"
                  rows={7}
                  value={text}
                  onChange={e => setText(e.target.value)}
                  placeholder={textContent.placeholders.textInput}
                  className="aipodcast-input"
                  style={{ borderRadius: '0.7rem', border: '1.5px solid #e3e3e3' }}
                />
              </Form.Group>
            </div>
          </Col>
          <Col xs={12} lg={5} className="d-flex flex-column">
            <div>
              <div className="aipodcast-form-label mb-2">{textContent.labels.voiceSelect}</div>
              <Form.Group>
                {isLoadingVoices ? (
                  <div className="d-flex align-items-center">
                    <Spinner animation="border" size="sm" className="me-2 aipodcast-spinner" />
                    <span>加载中...</span>
                  </div>
                ) : (
                  <Form.Select
                    value={selectedVoice}
                    onChange={e => setSelectedVoice(e.target.value)}
                    className="aipodcast-select"
                    style={{ borderRadius: '0.5rem', border: '1.5px solid #e3e3e3', minWidth: 120 }}
                  >
                    {voices.map(v => (
                      <option key={v.key} value={v.key}>
                        {v.desc || v.key}
                      </option>
                    ))}
                  </Form.Select>
                )}
              </Form.Group>
            </div>
          </Col>
        </Row>
        <Row className="g-4 mt-4 align-items-stretch">
          <Col xs={12}>
            <div style={{ background: '#f8fbff', borderRadius: '0.8rem', padding: '1.2rem 1rem', boxShadow: '0 1.5px 8px 0 rgba(0,123,255,0.04)' }}>
              <Row className="align-items-center flex-column flex-md-row">
                <Col xs={12} md={5} className="mb-3 mb-md-0">
                  <Button
                    type="submit"
                    className="w-100 aipodcast-btn-primary"
                    size="lg"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner as="span" animation="border" size="sm" className="me-2 aipodcast-spinner" />
                        生成中...
                      </>
                    ) : (
                      textContent.labels.submit
                    )}
                  </Button>
                </Col>
                <Col xs={12} md={7}>
                  <div className="audio-placeholder-center">
                    {audioUrl ? (
                      <audio ref={audioRef} controls className="w-100 aipodcast-audio" style={{ maxWidth: 420 }}>
                        <source src={audioUrl} type="audio/wav" />
                        您的浏览器不支持音频元素。
                      </audio>
                    ) : (
                      <p className="text-muted mb-0">
                        生成的音频将显示在这里。
                      </p>
                    )}
                  </div>
                </Col>
              </Row>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default TTSGenerator;