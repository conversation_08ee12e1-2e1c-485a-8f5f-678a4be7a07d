import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Spinner, Image, Button, Tabs, Tab } from 'react-bootstrap';

import SimpleToast from './SimpleToast';
import { getAIVideoDetail, replaceAsset, generateAIVideoPart2, getVideoStyles, updateAIVideoDetail } from '../services/api';
import { AIVideo } from '../types';
import { handleDownload, getTypeName } from '../utils';

interface DetailProps {
  id: number | string;
  onBack?: () => void;
}

const AIVideoDetail: React.FC<DetailProps> = ({ id, onBack }) => {
  // Tabs 默认激活 key，优先读取 localStorage
  // Tabs 默认激活 key，初始为 "base"，localStorage 读取移至 useEffect
  const [activeKey, setActiveKey] = useState<string>("base");

  // 修正 localStorage 访问，避免 SSR 报错
  useEffect(() => {
    if (typeof window !== "undefined" && window.localStorage) {
      const stored = window.localStorage.getItem(`aivideo-detail-tab-${id}`);
      if (stored) setActiveKey(stored);
    }
  }, [id]);
  // 时长格式化函数
  function formatDurationToSeconds(dur: number): string {
    if (typeof dur === 'number') return `${Math.floor(dur / 1000000)}秒`;
    return String(dur);
  }
  const [data, setData] = useState<AIVideo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [descPromoptList, setDescPromoptList] = useState<string[]>([]);
  const [captionList, setCaptionList] = useState<string[]>([]);

  // 编辑相关 state（优化：标题和文案独立编辑状态）
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingContent, setIsEditingContent] = useState(false);
  const [editedTitle, setEditedTitle] = useState<string>('');
  const [editedContent, setEditedContent] = useState<string>('');
  const [editLoadingTitle, setEditLoadingTitle] = useState(false);
  const [editLoadingContent, setEditLoadingContent] = useState(false);
  const [editResultTitle, setEditResultTitle] = useState<{ type: 'success' | 'error'; msg: string } | null>(null);
  const [editResultContent, setEditResultContent] = useState<{ type: 'success' | 'error'; msg: string } | null>(null);

  // 新增：视频风格相关 state
  const [videoStyles, setVideoStyles] = useState<{ type: string; name: string }[]>([]);
  const [videoStylesLoading, setVideoStylesLoading] = useState(false);
  const [videoStylesError, setVideoStylesError] = useState<string | null>(null);

  // 分镜图像预览
  const [previewImg, setPreviewImg] = useState<string | null>(null);

  // 三步法各自的状态
  const [step2Loading, setStep2Loading] = useState(false);
  const [step2Error, setStep2Error] = useState<string | null>(null);
  const [step2Success, setStep2Success] = useState<string | null>(null);

  const [step3Loading, setStep3Loading] = useState(false);
  const [step3Error, setStep3Error] = useState<string | null>(null);
  const [step3Success, setStep3Success] = useState<string | null>(null);

  const [step4Loading, setStep4Loading] = useState(false);
  const [step4Error, setStep4Error] = useState<string | null>(null);
  const [step4Success, setStep4Success] = useState<string | null>(null);

  // 图片素材按钮 loading 状态，key 用图片 url
  const [imgBtnLoading, setImgBtnLoading] = useState<{ [k: string]: boolean }>({});
  // 音频素材按钮 loading 状态，key 用音频 url
  const [audioBtnLoading, setAudioBtnLoading] = useState<{ [k: string]: boolean }>({});
  // 图片替换错误
  const [imgReplaceError, setImgReplaceError] = useState<string | null>(null);
  // 音频替换错误
  const [audioReplaceError, setAudioReplaceError] = useState<string | null>(null);
  // 下载错误
  const [downloadError, setDownloadError] = useState<string | null>(null);

  // 类型映射辅助函数已提取至 utils

  useEffect(() => {
    setLoading(true);
    setError(null);
    getAIVideoDetail(id)
      .then(res => {
        setData(res.data);
        setLoading(false);
        // 编辑状态下刷新数据时，重置编辑内容
        setEditedTitle(res.data?.title || '');
        setEditedContent(res.data?.content || '');
        // 分镜图像提示词和字幕受控 state 初始化
        if (Array.isArray(res.data?.storyboard_list)) {
                  // descPromoptList: 提取每项 desc_promopt 字段，若无则空字符串
                  const newDescPromoptList = res.data.storyboard_list.map((item: any) =>
                    typeof item === 'object' && item !== null
                      ? item.desc_promopt ?? ''
                      : ''
                  );
                  setDescPromoptList(newDescPromoptList);
                  // captionList: 提取每项 cap 字段，若无则空字符串
                  const newCaptionList = res.data.storyboard_list.map((item: any) =>
                    typeof item === 'object' && item !== null
                      ? item.cap ?? ''
                      : ''
                  );
                  setCaptionList(newCaptionList);
                } else {
                  setDescPromoptList([]);
                  setCaptionList([]);
                }
        // 调试：打印后端返回数据
        console.log('AIVideoDetail data:', res);
      })
      .catch(e => {
        setError(e.message || '获取AI视频详情失败');
        setLoading(false);
      });
    // 清理三步状态
    setStep2Loading(false);
    setStep2Error(null);
    setStep2Success(null);

    setStep3Loading(false);
    setStep3Error(null);
    setStep3Success(null);

    setStep4Loading(false);
    setStep4Error(null);
    setStep4Success(null);

    // 退出编辑模式时清理提示
    setIsEditingTitle(false);
    setIsEditingContent(false);
    setEditLoadingTitle(false);
    setEditLoadingContent(false);
    setEditResultTitle(null);
    setEditResultContent(null);
  }, [id]);

  // 新增：异步获取视频风格列表
  useEffect(() => {
    setVideoStylesLoading(true);
    setVideoStylesError(null);
    getVideoStyles()
      .then(res => {
        setVideoStyles(res || []);
        setVideoStylesLoading(false);
      })
      .catch(e => {
        setVideoStylesError(e.message || '获取视频风格失败');
        setVideoStylesLoading(false);
      });
  }, []);

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: 200 }}>
        <Spinner animation="border" />
      </div>
    );
  }

  if (error) {
    return <Alert variant="danger">{error}</Alert>;
  }

  if (!data) {
    return <Alert variant="warning">未找到该视频详情</Alert>;
  }

  return (
    <div className="aipodcast-card" style={{ width: '100%', padding: 10 }}>
      <div className="d-flex align-items-center justify-content-between mb-4">
        <div className="aipodcast-section-title mb-0">AI视频详情</div>
        <button
          className="aipodcast-btn-primary"
          style={{
            fontSize: 15,
            borderRadius: '0.7rem',
            padding: '0.35rem 1.2rem',
            minWidth: 96,
            marginLeft: 12,
            boxShadow: '0 2px 8px 0 rgba(0,123,255,0.08)'
          }}
          onClick={() => {
            if (onBack) {
              onBack();
            } else {
              window.location.pathname = '/aivideo';
            }
          }}
        >
          返回列表
        </button>
      </div>
      {/* 基础信息区：所有Tab均显示，放在标题和按钮下方、步骤按钮区上方 */}
      <div
        style={{
          background: '#f8fbff',
          borderRadius: '0.8rem',
          boxShadow: '0 1.5px 8px 0 rgba(0,123,255,0.04)',
          padding: '1.5rem 1.2rem 1.2rem 1.2rem',
          marginBottom: '1.5rem',
          border: '1.5px solid #e3e3e3'
        }}
      >
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '0.8rem 1.2rem',
            alignItems: 'start',
          }}
        >
          <div>
            <div className="aipodcast-form-label mb-1"><b>主题</b></div>
            <div style={{ wordBreak: 'break-all', fontWeight: 500 }}>{data.input}</div>
          </div>
          <div>
            <div className="aipodcast-form-label mb-1"><b>视频类型</b></div>
            <div style={{ wordBreak: 'break-all', fontWeight: 500 }}>{getTypeName(data.type, videoStyles)}</div>
          </div>
          <div>
            <div className="aipodcast-form-label mb-1"><b>标题</b></div>
            {isEditingTitle ? (
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <input
                  type="text"
                  className="form-control"
                  style={{ minWidth: 180, maxWidth: 320 }}
                  value={editedTitle}
                  onChange={e => setEditedTitle(e.target.value)}
                  disabled={editLoadingTitle}
                />
                <Button
                  variant="primary"
                  size="sm"
                  disabled={editLoadingTitle}
                  onClick={async () => {
                    setEditLoadingTitle(true);
                    setEditResultTitle(null);
                    try {
                      await updateAIVideoDetail(data.id, editedTitle, data.content);
                      const detailRes = await getAIVideoDetail(data.id);
                      setData(detailRes.data);
                      setEditedTitle(detailRes.data?.title || '');
                      setEditResultTitle({ type: 'success', msg: '标题保存成功！' });
                      setIsEditingTitle(false);
                    } catch (e: any) {
                      setEditResultTitle({ type: 'error', msg: e?.message || '标题保存失败' });
                    }
                    setEditLoadingTitle(false);
                  }}
                >
                  {editLoadingTitle ? <Spinner as="span" animation="border" size="sm" /> : '保存'}
                </Button>
                <Button
                  variant="outline-secondary"
                  size="sm"
                  disabled={editLoadingTitle}
                  onClick={() => {
                    setIsEditingTitle(false);
                    setEditedTitle(data.title || '');
                    setEditResultTitle(null);
                  }}
                >
                  取消
                </Button>
              </div>
            ) : (
              <div style={{ wordBreak: 'break-all', display: 'flex', alignItems: 'center', gap: 8 }}>
                <span>{data.title || '未命名视频'}</span>
                <Button
                  variant="outline-primary"
                  size="sm"
                  style={{ fontSize: 13, padding: '2px 12px' }}
                  onClick={() => {
                    setIsEditingTitle(true);
                    setEditedTitle(data.title || '');
                    setEditResultTitle(null);
                  }}
                >
                  编辑
                </Button>
                {/* 标题保存 toast 提示 */}
                <SimpleToast
                  show={!!editResultTitle}
                  type={editResultTitle?.type === 'success' ? 'success' : 'error'}
                  message={editResultTitle?.msg || ''}
                  onClose={() => setEditResultTitle(null)}
                />
              </div>
            )}
          </div>
          <div>
            <div className="aipodcast-form-label mb-1"><b>关键词</b></div>
            <div style={{ wordBreak: 'break-all' }}>{data.cap || '-'}</div>
          </div>
          <div>
            {typeof data.draft_url === 'string' && data.draft_url && (
              <>
                <div className="aipodcast-form-label mb-1"><b>草稿链接</b></div>
                <a href={data.draft_url} target="_blank" rel="noopener noreferrer">{data.draft_url}</a>
              </>
            )}
            {typeof data.debug_url === 'string' && data.debug_url && (
              <>
                <div className="aipodcast-form-label mb-1"><b>调试链接</b></div>
                <a href={data.debug_url} target="_blank" rel="noopener noreferrer">{data.debug_url}</a>
              </>
            )}
          </div>
        </div>
      </div>
      {/* 顶部多步法按钮始终显示在Tabs上方 */}
      <div className="mb-3">
        <div className="mb-2" style={{ fontSize: 13, color: '#888' }}>
          多步法：每一步独立生成，点击对应按钮执行。生成过程可能需要数十秒，请耐心等待。
        </div>
        {/* 优化后的按钮区域 */}
        <div
          className="aivideo-steps-area"
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: 8,
            background: '#f8fbff',
            borderRadius: 24,
            boxShadow: '0 1.5px 8px 0 rgba(0,123,255,0.04)',
            padding: '1.1rem 1.2rem',
            marginBottom: 8,
            border: '1.5px solid #e3e3e3',
          }}
        >
          {/* 步骤1：生成文案（始终显示，禁用，无点击逻辑，仅流程展示） */}
          <Button
            className="aipodcast-btn-primary"
            style={{
              flex: 1,
              fontWeight: 500,
              fontSize: 16,
              borderRadius: 20,
              boxShadow: '0 2px 8px 0 rgba(0,123,255,0.08)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 8,
            }}
            disabled={true}
            variant="secondary"
            aria-label="生成文案"
          >
            <>
              生成文案
              {typeof data.content === 'string' && data.content.trim() !== '' && (
                <span className="ms-2" style={{
                  color: '#28a745',
                  fontWeight: 700,
                  fontSize: 18,
                  verticalAlign: 'middle',
                  marginLeft: 2,
                }} title="已完成">✔️</span>
              )}
            </>
          </Button>
          {/* 箭头分隔符 */}
          <span
            style={{
              margin: '0 0px',
              fontSize: 24,
              color: '#b0b8c9',
              userSelect: 'none',
              fontWeight: 600,
              verticalAlign: 'middle',
            }}
          >
            &#8594;
          </span>
          {/* 步骤2按钮：生成图像 */}
          <Button
            className="aipodcast-btn-primary"
            style={{
              flex: 1,
              fontWeight: 500,
              fontSize: 16,
              borderRadius: 20,
              boxShadow: '0 2px 8px 0 rgba(0,123,255,0.08)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 8,
            }}
            disabled={
              step2Loading ||
              !!step2Error ||
              (Array.isArray(data.image_list) && data.image_list.length > 0)
            }
            variant={
              step2Error
                ? 'danger'
                : step2Success
                ? 'success'
                : step2Loading
                ? 'secondary'
                : 'primary'
            }
            onClick={async () => {
              setStep2Loading(true);
              setStep2Error(null);
              setStep2Success(null);
              try {
                const res = data.content
                  ? await generateAIVideoPart2(data.id, data.content, 2)
                  : await generateAIVideoPart2(data.id, undefined, 2);
                if (res.status === 200) {
                  setStep2Success(`第2步生成成功，正在刷新详情...`);
                  getAIVideoDetail(data.id)
                    .then(res2 => {
                      setData(res2.data);
                      setStep2Success(`第2步生成成功！`);
                      setStep2Loading(false);
                    })
                    .catch(e => {
                      setStep2Error('生成成功但刷新详情失败: ' + (e.message || ''));
                      setStep2Loading(false);
                    });
                } else {
                  setStep2Error('生成失败');
                  setStep2Loading(false);
                }
              } catch (error: any) {
                setStep2Error(error?.message || '生成失败');
                setStep2Loading(false);
              }
            }}
            aria-label="生成视频第2步"
          >
            {step2Loading ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                正在生成视频（第2步）...
              </>
            ) : step2Error ? (
              '生成图像失败'
            ) : (
              <>
                {'生成图像'}
                {Array.isArray(data.image_list) && data.image_list.length > 0 && !step2Loading && !step2Error && (
                  <span className="ms-2" style={{
                    color: '#28a745',
                    fontWeight: 700,
                    fontSize: 18,
                    verticalAlign: 'middle',
                    marginLeft: 2,
                  }} title="已完成">✔️</span>
                )}
              </>
            )}
          </Button>
          {/* 箭头分隔符 */}
          <span
            style={{
              margin: '0 0px',
              fontSize: 24,
              color: '#b0b8c9',
              userSelect: 'none',
              fontWeight: 600,
              verticalAlign: 'middle',
            }}
          >
            &#8594;
          </span>
          {/* 步骤3按钮：生成音频 */}
          <Button
            className="aipodcast-btn-primary"
            style={{
              flex: 1,
              fontWeight: 500,
              fontSize: 16,
              borderRadius: 20,
              boxShadow: '0 2px 8px 0 rgba(0,123,255,0.08)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 8,
            }}
            disabled={
              step3Loading ||
              !!step3Error ||
              (Array.isArray(data.audio_list) && data.audio_list.length > 0)
            }
            variant={
              step3Error
                ? 'danger'
                : step3Success
                ? 'success'
                : step3Loading
                ? 'secondary'
                : 'primary'
            }
            onClick={async () => {
              setStep3Loading(true);
              setStep3Error(null);
              setStep3Success(null);
              try {
                const res = data.content
                  ? await generateAIVideoPart2(data.id, data.content, 3)
                  : await generateAIVideoPart2(data.id, undefined, 3);
                if (res.status === 200) {
                  setStep3Success(`第3步生成成功，正在刷新详情...`);
                  getAIVideoDetail(data.id)
                    .then(res2 => {
                      setData(res2.data);
                      setStep3Success(`第3步生成成功！`);
                      setStep3Loading(false);
                    })
                    .catch(e => {
                      setStep3Error('生成成功但刷新详情失败: ' + (e.message || ''));
                      setStep3Loading(false);
                    });
                } else {
                  setStep3Error('生成失败');
                  setStep3Loading(false);
                }
              } catch (error: any) {
                setStep3Error(error?.message || '生成失败');
                setStep3Loading(false);
              }
            }}
            aria-label="生成视频第3步"
          >
            {step3Loading ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                正在生成视频（第3步）...
              </>
            ) : step3Error ? (
              '生成音频失败'
            ) : (
              <>
                {'生成音频'}
                {Array.isArray(data.audio_list) && data.audio_list.length > 0 && !step3Loading && !step3Error && (
                  <span className="ms-2" style={{
                    color: '#28a745',
                    fontWeight: 700,
                    fontSize: 18,
                    verticalAlign: 'middle',
                    marginLeft: 2,
                  }} title="已完成">✔️</span>
                )}
              </>
            )}
          </Button>
          {/* 箭头分隔符 */}
          <span
            style={{
              margin: '0 0px',
              fontSize: 24,
              color: '#b0b8c9',
              userSelect: 'none',
              fontWeight: 600,
              verticalAlign: 'middle',
            }}
          >
            &#8594;
          </span>
          {/* 步骤4按钮 */}
          <Button
            className="aipodcast-btn-primary"
            style={{
              flex: 1,
              fontWeight: 500,
              fontSize: 16,
              borderRadius: 20,
              boxShadow: '0 2px 8px 0 rgba(0,123,255,0.08)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 8,
            }}
            disabled={step4Loading}
            variant={
              step4Error
                ? 'danger'
                : step4Success
                ? 'success'
                : step4Loading
                ? 'secondary'
                : 'primary'
            }
            onClick={async () => {
              setStep4Loading(true);
              setStep4Error(null);
              setStep4Success(null);
              try {
                const res = data.content
                  ? await generateAIVideoPart2(data.id, data.content, 4)
                  : await generateAIVideoPart2(data.id, undefined, 4);
                if (res.status === 200) {
                  setStep4Success(`第4步生成成功，正在刷新详情...`);
                  getAIVideoDetail(data.id)
                    .then(res2 => {
                      setData(res2.data);
                      setStep4Success(`第4步生成成功！`);
                      setStep4Loading(false);
                    })
                    .catch(e => {
                      setStep4Error('生成成功但刷新详情失败: ' + (e.message || ''));
                      setStep4Loading(false);
                    });
                } else {
                  setStep4Error('生成失败');
                  setStep4Loading(false);
                }
              } catch (error: any) {
                setStep4Error(error?.message || '生成失败');
                setStep4Loading(false);
              }
            }}
            aria-label="生成视频第4步"
          >
            {step4Loading ? (
              <>
                <Spinner as="span" animation="border" size="sm" className="me-2" />
                正在生成视频（第4步）...
              </>
            ) : step4Error ? (
              '合成视频失败'
            ) : (
              <>
                {'合成视频'}
                {typeof data.draft_url === 'string' && data.draft_url && !step4Loading && !step4Error && (
                  <span className="ms-2" style={{
                    color: '#28a745',
                    fontWeight: 700,
                    fontSize: 18,
                    verticalAlign: 'middle',
                    marginLeft: 2,
                  }} title="已完成">✔️</span>
                )}
              </>
            )}
          </Button>
        </div>
        {/* 各按钮独立状态提示 */}
        {step2Error && (
          <Alert
            variant="danger"
            onClose={() => setStep2Error(null)}
            dismissible
            className="mb-2"
          >
            {step2Error}
          </Alert>
        )}
        {step2Success && (
          <Alert
            variant="success"
            onClose={() => setStep2Success(null)}
            dismissible
            className="mb-2"
          >
            {step2Success}
          </Alert>
        )}
        {step3Error && (
          <Alert
            variant="danger"
            onClose={() => setStep3Error(null)}
            dismissible
            className="mb-2"
          >
            {step3Error}
          </Alert>
        )}
        {step3Success && (
          <Alert
            variant="success"
            onClose={() => setStep3Success(null)}
            dismissible
            className="mb-2"
          >
            {step3Success}
          </Alert>
        )}
        {step4Error && (
          <Alert
            variant="danger"
            onClose={() => setStep4Error(null)}
            dismissible
            className="mb-2"
          >
            {step4Error}
          </Alert>
        )}
        {step4Success && (
          <Alert
            variant="success"
            onClose={() => setStep4Success(null)}
            dismissible
            className="mb-2"
          >
            {step4Success}
          </Alert>
        )}
      </div>
      <Tabs
        defaultActiveKey={activeKey}
        id="aivideo-detail-tabs"
        className="mb-3"
        onSelect={(eventKey) => {
          if (eventKey) {
            localStorage.setItem(`aivideo-detail-tab-${id}`, String(eventKey));
            setActiveKey(String(eventKey));
          }
        }}
      >
        <Tab eventKey="base" title="视频文案">
          {/* 错误/成功提示 */}
          {downloadError && (
            <Alert
              variant="danger"
              onClose={() => setDownloadError(null)}
              dismissible
              className="mb-2"
            >
              {downloadError}
            </Alert>
          )}
          {step2Error && (
            <Alert
              variant="danger"
              onClose={() => setStep2Error(null)}
              dismissible
              className="mb-2"
            >
              {step2Error}
            </Alert>
          )}
          {step2Success && (
            <Alert
              variant="success"
              onClose={() => setStep2Success(null)}
              dismissible
              className="mb-2"
            >
              {step2Success}
            </Alert>
          )}
          {step3Error && (
            <Alert
              variant="danger"
              onClose={() => setStep3Error(null)}
              dismissible
              className="mb-2"
            >
              {step3Error}
            </Alert>
          )}
          {step3Success && (
            <Alert
              variant="success"
              onClose={() => setStep3Success(null)}
              dismissible
              className="mb-2"
            >
              {step3Success}
            </Alert>
          )}
          {step4Error && (
            <Alert
              variant="danger"
              onClose={() => setStep4Error(null)}
              dismissible
              className="mb-2"
            >
              {step4Error}
            </Alert>
          )}
          {step4Success && (
            <Alert
              variant="success"
              onClose={() => setStep4Success(null)}
              dismissible
              className="mb-2"
            >
              {step4Success}
            </Alert>
          )}
          <div style={{
            background: '#f8fbff',
            borderRadius: '0.8rem',
            boxShadow: '0 1.5px 8px 0 rgba(0,123,255,0.04)',
            padding: '1.5rem 1.2rem 1.2rem 1.2rem',
            marginBottom: '1.5rem',
            border: '1.5px solid #e3e3e3'
          }}>
            {/* 视频文案内容 */}
            <div className="mb-3">
              <div className="aipodcast-form-label mb-1"><b>视频文案</b></div>
              {isEditingContent ? (
                <div>
                  <textarea
                                      className="form-control"
                                      style={{
                                        minHeight: 400,
                                        fontSize: 15,
                                        color: '#333',
                                        wordBreak: 'break-all',
                                        padding: '4px 8px',
                                        borderRadius: 6,
                                        border: '1px solid #e3e3e3',
                                        background: '#f8f9fa', // 与展示区一致
                                        whiteSpace: 'pre-wrap',
                                        lineHeight: 1.7,
                                        resize: 'vertical'
                                      }}
                                      value={editedContent}
                                      onChange={e => setEditedContent(e.target.value)}
                                      disabled={editLoadingContent}
                                    />
                  <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8, marginTop: 8 }}>
                    <Button
                      variant="primary"
                      size="sm"
                      disabled={editLoadingContent}
                      onClick={async () => {
                        setEditLoadingContent(true);
                        setEditResultContent(null);
                        try {
                          await updateAIVideoDetail(data.id, data.title, editedContent);
                          const detailRes = await getAIVideoDetail(data.id);
                          setData(detailRes.data);
                          setEditedContent(detailRes.data?.content || '');
                          setEditResultContent({ type: 'success', msg: '视频文案保存成功！' });
                          setIsEditingContent(false);
                        } catch (e: any) {
                          setEditResultContent({ type: 'error', msg: e?.message || '视频文案保存失败' });
                        }
                        setEditLoadingContent(false);
                      }}
                    >
                      {editLoadingContent ? <Spinner as="span" animation="border" size="sm" /> : '保存'}
                    </Button>
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      disabled={editLoadingContent}
                      onClick={() => {
                        setIsEditingContent(false);
                        setEditedContent(data.content || '');
                        setEditResultContent(null);
                      }}
                    >
                      取消
                    </Button>
                  </div>
                </div>
              ) : (
                <div style={{
                  minHeight: 60,
                  fontSize: 15,
                  color: '#333',
                  wordBreak: 'break-all',
                  padding: '4px 8px',
                  borderRadius: 6,
                  border: '1px solid #e3e3e3',
                  background: '#f8f9fa',
                  whiteSpace: 'pre-wrap',
                  lineHeight: 1.7
                }}>
                  {data.content}
                  <div style={{ marginTop: 8 }}>
                    <Button
                      variant="outline-primary"
                      size="sm"
                      style={{ fontSize: 13, padding: '2px 12px' }}
                      onClick={() => {
                        setIsEditingContent(true);
                        setEditedContent(data.content || '');
                        setEditResultContent(null);
                      }}
                    >
                      编辑视频文案
                    </Button>
                    {/* 文案保存 toast 提示 */}
                    <SimpleToast
                      show={!!editResultContent}
                      type={editResultContent?.type === 'success' ? 'success' : 'error'}
                      message={editResultContent?.msg || ''}
                      onClose={() => setEditResultContent(null)}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </Tab>
        <Tab eventKey="image" title="分镜图像">
          {/* 分镜图像列表重构 */}
          {Array.isArray(data.image_list) && Array.isArray(data.storyboard_list) && data.image_list.length > 0 ? (
            <>
              <div className="aipodcast-form-label mb-2"><b>分镜图像列表</b></div>
              <div style={{ width: '100%', overflowX: 'auto' }}>
                <table className="table table-bordered" style={{ minWidth: 600, background: '#f8fbff', borderRadius: 8 }}>
                  <thead>
                    <tr>
                      <th style={{ width: 180 }}>分镜描述</th>
                      <th style={{ width: 220 }}>图像提示词</th>
                      <th style={{ width: 220 }}>分镜图像</th>
                      <th style={{ width: 180 }}>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.image_list.map((img, idx) => {
                      // 获取分镜描述
                      let descDetail = '';
                      if (Array.isArray(data?.storyboard_list) && data?.storyboard_list[idx]) {
                        if (typeof data.storyboard_list[idx] === 'object') {
                          descDetail = data.storyboard_list[idx]?.desc || '';
                        } else {
                          descDetail = '';
                        }
                      }

                      return (
                        <tr key={idx}>
                          <td style={{ width: 180, wordBreak: 'break-all', fontSize: 14 }}>{descDetail}</td>
                          <td style={{ width: 220, padding: 0 }}>
                            <textarea
                              className="form-control"
                              value={descPromoptList[idx] ?? ''}
                              rows={3}
                              style={{
                                minHeight: 100, // 最小高度
                                maxHeight: 220, // 最大高度，超出可滚动
                                fontSize: 14,
                                color: '#333',
                                wordBreak: 'break-all',
                                whiteSpace: 'pre-wrap',
                                padding: '4px 8px',
                                borderRadius: 6,
                                border: '1px solid #e3e3e3',
                                background: '#f8f9fa',
                                lineHeight: 1.6,
                                resize: 'vertical', // 允许垂直缩放
                                width: '100%',
                                boxSizing: 'border-box',
                                overflow: 'auto' // 超出显示滚动条
                              }}
                              onChange={e => {
                                // 实时更新分镜图像提示词 state，仅本地生效
                                const newEditValueList = [...descPromoptList];
                                newEditValueList[idx] = e.target.value;
                                setDescPromoptList(newEditValueList);
                              }}
                            />
                          </td>
                          <td style={{ width: 220, textAlign: 'center', verticalAlign: 'middle' }}>
                            {typeof img === 'string' && img.startsWith('http') ? (
                              <Image
                                src={img}
                                alt={`img${idx}`}
                                style={{
                                  width: '90%',
                                  height: 'auto',
                                  maxHeight: 240,
                                  objectFit: 'contain',
                                  borderRadius: '0.5rem',
                                  cursor: 'pointer',
                                  background: '#fff'
                                }}
                                onClick={() => setPreviewImg(img)}
                              />
                            ) : (
                              <div style={{ fontSize: 13, color: '#888' }}>{JSON.stringify(img)}</div>
                            )}
                          </td>
                          <td style={{ width: 180, textAlign: 'center', verticalAlign: 'middle' }}>
                            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 8 }}>
                              <Button
                                variant="outline-primary"
                                size="sm"
                                disabled={!!imgBtnLoading[img]}
                                onClick={async () => {
                                  setImgBtnLoading(prev => ({ ...prev, [img]: true }));
                                  setImgReplaceError(null);
                                  try {
                                    // replaceAsset 需传 data.id, idx, img, assetId
                                    await replaceAsset(data.id, 'image', descPromoptList[idx], idx);
                                    // 刷新详情
                                    const res = await getAIVideoDetail(data.id);
                                    setData(res.data);
                                  } catch (e: any) {
                                    setImgReplaceError(e?.message || '图片替换失败');
                                  }
                                  setImgBtnLoading(prev => ({ ...prev, [img]: false }));
                                }}
                              >
                                {imgBtnLoading[img] ? (
                                  <Spinner as="span" animation="border" size="sm" />
                                ) : '重新生成图片'}
                              </Button>
                              <Button
                                variant="outline-success"
                                size="sm"
                                onClick={async () => {
                                  try {
                                    await handleDownload(img);
                                  } catch (e: any) {
                                    setDownloadError(e?.message || '下载失败');
                                  }
                                }}
                              >
                                下载
                              </Button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
              {/* 图片预览 Modal */}
              {previewImg && (
                <div
                  style={{
                    position: 'fixed',
                    top: 0, left: 0, right: 0, bottom: 0,
                    background: 'rgba(0,0,0,0.6)',
                    zIndex: 9999,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                  onClick={() => setPreviewImg(null)}
                >
                  <img
                    src={previewImg}
                    alt="预览"
                    style={{
                      maxWidth: '80vw',
                      maxHeight: '80vh',
                      borderRadius: 12,
                      boxShadow: '0 4px 24px rgba(0,0,0,0.18)'
                    }}
                  />
                </div>
              )}
              {/* 操作错误提示 */}
              {imgReplaceError && (
                <Alert
                  variant="danger"
                  onClose={() => setImgReplaceError(null)}
                  dismissible
                  className="mt-2"
                >
                  {imgReplaceError}
                </Alert>
              )}
              {downloadError && (
                <Alert
                  variant="danger"
                  onClose={() => setDownloadError(null)}
                  dismissible
                  className="mt-2"
                >
                  {downloadError}
                </Alert>
              )}
            </>
          ) : (
            <div className="mb-3" style={{ color: '#888' }}>暂无分镜图像信息</div>
          )}
        </Tab>
        <Tab eventKey="audio" title="音频信息">
          {/* 音频信息表格化展示 */}
          {Array.isArray(data.audio_list) && data.audio_list.length > 0 ? (
            <>
              <div className="aipodcast-form-label mb-2"><b>音频列表</b></div>
              <div style={{ width: '100%', overflowX: 'auto' }}>
                <table className="table table-bordered" style={{ minWidth: 600, background: '#f8fbff', borderRadius: 8 }}>
                  <thead>
                    <tr>
                      <th style={{ width: 300 }}>字幕</th>
                      <th style={{ width: 200 }}>音频</th>
                      <th style={{ width: 180 }}>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.audio_list.map((audio, idx) => {
                                            // assetId 用 idx
                                            const assetId = idx;
                                            // 时长
                                            let duration = '';
                                            if (Array.isArray(data.duration_list) && data.duration_list[idx]) {
                                              duration = String(data.duration_list[idx]);
                                            }
                                            return (
                                              <tr key={idx}>
                                                <td style={{ width: 180, padding: 0 }}>
                                                  <textarea
                                                    className="form-control"
                                                    value={captionList[idx] ?? ''}
                                                    rows={1}
                                                    style={{
                                                      minHeight: 100, // 最小高度
                                                      maxHeight: 220, // 最大高度，超出可滚动
                                                      fontSize: 14,
                                                      color: '#333',
                                                      wordBreak: 'break-all',
                                                      whiteSpace: 'pre-wrap',
                                                      padding: '4px 8px',
                                                      borderRadius: 6,
                                                      border: '1px solid #e3e3e3',
                                                      background: '#f8f9fa',
                                                      lineHeight: 1.6,
                                                      resize: 'vertical', // 允许垂直缩放
                                                      width: '100%',
                                                      boxSizing: 'border-box',
                                                      overflow: 'auto' // 超出显示滚动条
                                                    }}
                                                    onChange={e => {
                                                      // 实时更新字幕 state，仅本地生效
                                                      const newEditValueList = [...captionList];
                                                      newEditValueList[idx] = e.target.value;
                                                      setCaptionList(newEditValueList);
                                                    }}
                                                  />
                                                </td>
                                                <td style={{ width: 320, wordBreak: 'break-all', fontSize: 14 }}>
                                                  <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 12 }}>
                                                    {typeof audio === 'string' && audio.startsWith('http') ? (
                                                      <>
                                                        <audio controls src={audio} style={{ width: 180, borderRadius: '0.5rem', background: '#f8f9fa' }} />
                                                        {duration ? (
                                                          <span
                                                            style={{
                                                              fontSize: 15,
                                                              color: '#1976d2',
                                                              fontWeight: 600,
                                                              background: '#e3f2fd',
                                                              borderRadius: '0.4rem',
                                                              padding: '2px 10px',
                                                              display: 'inline-block',
                                                              letterSpacing: 1,
                                                              marginLeft: 8,
                                                            }}
                                                          >
                                                            时长：{Array.isArray(data.duration_list) ? formatDurationToSeconds(data.duration_list[idx]) : ''}
                                                          </span>
                                                        ) : null}
                                                      </>
                                                    ) : (
                                                      <span style={{ fontSize: 13, color: '#888' }}>{JSON.stringify(audio)}</span>
                                                    )}
                                                  </div>
                                                </td>
                                                <td style={{ width: 180, textAlign: 'center', verticalAlign: 'middle' }}>
                                                  <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 8 }}>
                                                    {/* 保留原音频操作按钮 */}
                                                    <Button
                                                      variant="outline-primary"
                                                      size="sm"
                                                      disabled={!!audioBtnLoading[audio]}
                                                      onClick={async () => {
                                                        setAudioBtnLoading(prev => ({ ...prev, [audio]: true }));
                                                        setAudioReplaceError(null);
                                                        try {
                                                          await replaceAsset(data.id, 'audio', captionList[idx], assetId);
                                                          // 刷新详情
                                                          const res = await getAIVideoDetail(data.id);
                                                          setData(res.data);
                                                        } catch (e: any) {
                                                          setAudioReplaceError(e?.message || '音频替换失败');
                                                        }
                                                        setAudioBtnLoading(prev => ({ ...prev, [audio]: false }));
                                                      }}
                                                    >
                                                      {audioBtnLoading[audio] ? (
                                                        <Spinner as="span" animation="border" size="sm" />
                                                      ) : '重新生成音频'}
                                                    </Button>
                                                    <Button
                                                      variant="outline-success"
                                                      size="sm"
                                                      onClick={async () => {
                                                        try {
                                                          await handleDownload(audio);
                                                        } catch (e: any) {
                                                          setDownloadError(e?.message || '下载失败');
                                                        }
                                                      }}
                                                    >
                                                      下载
                                                    </Button>
                                                  </div>
                                                </td>
                                              </tr>
                                            );
                                          })}
                  </tbody>
                </table>
              </div>
              {audioReplaceError && (
                <Alert
                  variant="danger"
                  onClose={() => setAudioReplaceError(null)}
                  dismissible
                  className="mt-2"
                >
                  {audioReplaceError}
                </Alert>
              )}
              {downloadError && (
                <Alert
                  variant="danger"
                  onClose={() => setDownloadError(null)}
                  dismissible
                  className="mt-2"
                >
                  {downloadError}
                </Alert>
              )}
            </>
          ) : (
            <div className="mb-3" style={{ color: '#888' }}>暂无音频信息</div>
          )}
        </Tab>
      </Tabs>
    </div>
  );
};

export default AIVideoDetail;