'use client';

import Link from 'next/link';

type Props = {
  searchParams: {
    error?: string;
  };
};

export default function ErrorPage({ searchParams }: Props) {
  const error = searchParams?.error;
  
  let errorMessage = '认证过程中发生错误';
  if (error === 'AccessDenied') {
    errorMessage = '访问被拒绝，您可能没有足够的权限';
  } else if (error === 'Configuration') {
    errorMessage = '服务器配置错误，请联系管理员';
  } else if (error === 'OAuthSignin') {
    errorMessage = '登录过程初始化失败';
  } else if (error === 'OAuthCallback') {
    errorMessage = '登录回调处理失败';
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-light-primary dark:bg-dark-primary">
      <div className="p-8 rounded-lg bg-light-secondary dark:bg-dark-secondary shadow-lg max-w-md w-full">
        <h1 className="text-2xl font-semibold mb-4 text-center dark:text-white">
          认证错误
        </h1>
        <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
          {errorMessage}
        </p>
        <div className="flex flex-col gap-4">
          <Link
            href="/auth/signin"
            className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded text-center transition-colors"
          >
            重新登录
          </Link>
          <Link
            href="/"
            className="border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded text-center transition-colors"
          >
            返回首页
          </Link>
        </div>
      </div>
    </div>
  );
}