'use client';

import { Inter } from 'next/font/google';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../styles/globals.css';
import '../styles/app.css';
import { SessionProvider } from 'next-auth/react';
import React from 'react';
import { Container, Row, Col, Navbar, Nav, Dropdown } from 'react-bootstrap';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';

const inter = Inter({ subsets: ['latin'] });

// 用户信息组件
const UserInfo: React.FC = () => {
  const { data: session } = useSession();
  if (
    process.env.NEXT_PUBLIC_SSO_ENABLED !== 'true' ||
    !session?.user
  ) return null;
  return (
    <Dropdown align="end">
      <Dropdown.Toggle variant="outline-secondary" id="user-dropdown" size="sm">
        {session.user.name || session.user.email || '用户'}
      </Dropdown.Toggle>
      <Dropdown.Menu>
        <Dropdown.Item onClick={() => signOut({ callbackUrl: '/' })}>
          登出
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
};

// 侧边栏菜单项
const menuItems = [
  { path: '/aivideo', label: 'AI视频' },
  { path: '/podcast', label: '播客生成' },
  { path: '/tts', label: 'TTS生成' },
  { path: '/history', label: '历史记录' },
];

function LayoutShell({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  return (
    <Container fluid className="aipodcast-root-container p-0">
      {/* 顶部导航栏 */}
      <Navbar bg="light" variant="light" expand="lg" className="aipodcast-topbar">
        <Navbar.Brand className="aipodcast-navbar-brand">
          <span className="aipodcast-navbar-icon" role="img" aria-label="AI工作台">💡</span>
          {'AI 工作台'}
        </Navbar.Brand>
        <Nav className="ms-auto">
          <UserInfo />
        </Nav>
      </Navbar>
      {/* 主体区域 */}
      <Row className="aipodcast-mainrow g-0">
        {/* 侧边栏 */}
        <Col xs={2} md={2} className="aipodcast-sidebar bg-light border-end" style={{ minWidth: 200, maxWidth: 240 }}>
          <Nav className="flex-column py-4">
            {menuItems.map(item => (
              <Nav.Link
                as={Link}
                href={item.path}
                key={item.path}
                className={`aipodcast-sidebar-item mb-2${pathname === item.path ? ' active' : ''}`}
                style={{ fontWeight: 600 }}
              >
                {item.label}
              </Nav.Link>
            ))}
          </Nav>
        </Col>
        {/* 主内容区 */}
        <Col xs={10} md={10} className="aipodcast-maincontent">
          {children}
        </Col>
      </Row>
    </Container>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SessionProvider>
            <LayoutShell>
              {children}
            </LayoutShell>
        </SessionProvider>
      </body>
    </html>
  );
}