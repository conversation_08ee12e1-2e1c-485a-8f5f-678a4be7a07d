from fastapi import Fast<PERSON><PERSON>, File, UploadFile, Request
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Optional
import io
import os
from dotenv import load_dotenv
from huggingface_hub import snapshot_download

# 加载环境变量
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), ".env"), override=True)
MODEL_REPO_ID = os.getenv("MODEL_REPO_ID", "jzq11111/aipodcast")
MODEL_LOCAL_DIR = os.getenv("MODEL_LOCAL_DIR", "./resources/")

# 启动时自动下载模型权重
snapshot_download(repo_id=MODEL_REPO_ID, local_dir=MODEL_LOCAL_DIR)
print("Model weights are ready.")

# 启动时自动加载模型
from local_inference.inference import AudioModel
AudioModel.get_instance()
print("AI model loaded successfully.")

# 补全音频文件路径
with open(os.path.join(os.path.dirname(__file__), "role_audio", "role_config.json"), 'r', encoding='utf-8') as f:
    ROLE_AUDIO_CONFIG = json.load(f)

for lang in ROLE_AUDIO_CONFIG:
    for role in ROLE_AUDIO_CONFIG[lang]:
        ROLE_AUDIO_CONFIG[lang][role]["audio_path"] = os.path.join(
            os.path.dirname(__file__), 
            "role_audio",
            ROLE_AUDIO_CONFIG[lang][role]["audio_path"]
        )
        
app = FastAPI(
    title="Local Inference API",
    description="Local Inference Service",
    version="0.1.0"
)

# 允许跨域（如需前端联调）
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/infer")
async def infer(request: Request, file: Optional[UploadFile] = File(None)):
    # 支持 JSON 或文件上传
    if file:
        # 返回 mock 文件流
        content = b"mock audio result"
        return StreamingResponse(io.BytesIO(content), media_type="application/octet-stream", headers={"Content-Disposition": "attachment; filename=mock_result.bin"})
    else:
        try:
            data = await request.json()
        except Exception:
            data = {}
        # 返回 mock 推理结果 JSON
        return JSONResponse(content={"result": "mock result", "input": data})

@app.get("/health")
async def health():
    return JSONResponse(content={"status": "ok", "msg": "service healthy"})

@app.post("/download_model")
async def download_model(request: Request):
    try:
        data = await request.json()
    except Exception:
        data = {}
    # 返回 mock 状态
    return JSONResponse(content={"status": "success", "msg": "model download mock", "input": data})

@app.get("/list_models")
async def list_models():
    # 返回 mock 模型列表
    return JSONResponse(content={
        "models": [
            {"name": "mock_model_1", "version": "1.0"},
            {"name": "mock_model_2", "version": "2.0"}
        ]
    })

# 仅用于 uvicorn 启动
# 启动命令：uvicorn main:app --host 0.0.0.0 --port 8001