FROM python:3.10-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libsndfile1 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 复制项目文件
COPY requirements.txt .
COPY app.py .
COPY inference.py .
COPY modules/ ./modules/

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建模型目录
RUN mkdir -p ./resources

# 设置环境变量
ENV PORT=5008
ENV HOST=0.0.0.0
ENV DEBUG=False
ENV MODEL_REPO_ID=jzq11111/aipodcast
ENV MODEL_LOCAL_DIR=./resources/
ENV MAX_TOKENS_PER_TURN=2500
ENV PYTHONPATH=..

# 暴露端口
EXPOSE 5008

# 启动应用
CMD ["python", "main.py"]