# 模型文件
*.safetensors
*.pt
resources/

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
**.next

# Python
**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.env

# 前端
node_modules/
/frontend/build/
/frontend/.env
/frontend/.env.local
/frontend/.env.development.local
/frontend/.env.test.local
/frontend/.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 临时文件和上传
tmp*/
uploads/
*.gradio

# CUDA编译文件
modules/audio_detokenizer/vocoder/alias_free_activation/cuda/build/

# 日志
*.log
